/* ===== MEDICAL 3D COMPONENTS ===== */
/* Advanced 3D visualizations for medical education website */

class Medical3DComponents {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.globe = null;
    this.dnaHelix = null;
    this.animationId = null;
    this.isMobile = window.innerWidth < 768;
    this.isWebGLSupported = this.checkWebGLSupport();
    this.init();
  }

  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  async init() {
    console.log('🚀 Initializing Medical 3D Components...');

    // Check if Three.js is available and WebGL is supported
    if (typeof THREE === 'undefined') {
      console.warn('Three.js not loaded, using fallback');
      this.createFallbackVisuals();
      return;
    }

    if (!this.isWebGLSupported) {
      console.warn('WebGL not supported, using fallback');
      this.createFallbackVisuals();
      return;
    }

    try {
      this.setupScene();
      this.createInteractiveGlobe();
      this.createEnhancedDNA();
      this.setupControls();
      this.animate();
      this.setupResponsive();
      console.log('✅ Medical 3D Components initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize 3D components:', error);
      this.createFallbackVisuals();
    }
  }

  setupScene() {
    // Create scene with medical theme
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x0a0a1a);
    this.scene.fog = new THREE.Fog(0x0a0a1a, 30, 100);

    // Create camera with proper aspect ratio for globe container
    const container = document.getElementById('globe-container');
    if (!container) {
      throw new Error('Globe container not found');
    }

    const containerRect = container.getBoundingClientRect();
    const aspect = containerRect.width / containerRect.height;
    this.camera = new THREE.PerspectiveCamera(60, aspect, 0.1, 1000);
    this.camera.position.set(0, 0, 25);

    // Create optimized renderer
    this.renderer = new THREE.WebGLRenderer({
      antialias: !this.isMobile,
      alpha: true,
      powerPreference: this.isMobile ? 'low-power' : 'high-performance'
    });

    this.renderer.setSize(containerRect.width, containerRect.height);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.isMobile ? 1 : 2));
    this.renderer.shadowMap.enabled = !this.isMobile;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.outputEncoding = THREE.sRGBEncoding;

    // Clear loading state and add renderer to globe container
    container.innerHTML = '';
    container.appendChild(this.renderer.domElement);

    // Setup lighting
    this.setupLighting();
  }

  setupLighting() {
    // Ambient light for overall illumination
    const ambientLight = new THREE.AmbientLight(0x2563eb, 0.4);
    this.scene.add(ambientLight);

    // Main directional light (sun-like)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(30, 30, 30);
    if (!this.isMobile) {
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 1024;
      directionalLight.shadow.mapSize.height = 1024;
      directionalLight.shadow.camera.near = 0.1;
      directionalLight.shadow.camera.far = 100;
    }
    this.scene.add(directionalLight);

    // Medical-themed accent lights
    const medicalLight1 = new THREE.PointLight(0x0891b2, 0.6, 50);
    medicalLight1.position.set(15, 10, 15);
    this.scene.add(medicalLight1);

    const medicalLight2 = new THREE.PointLight(0x2563eb, 0.4, 40);
    medicalLight2.position.set(-15, -10, 15);
    this.scene.add(medicalLight2);

    // Rim light for dramatic effect
    const rimLight = new THREE.DirectionalLight(0x0891b2, 0.3);
    rimLight.position.set(-30, 0, -30);
    this.scene.add(rimLight);
  }

  createInteractiveGlobe() {
    console.log('Creating interactive globe with FEFU location...');

    // Create Earth geometry with higher detail for better quality
    const globeRadius = 8;
    const geometry = new THREE.SphereGeometry(
      globeRadius,
      this.isMobile ? 32 : 64,
      this.isMobile ? 32 : 64
    );

    // Create realistic Earth material
    const material = new THREE.MeshPhongMaterial({
      color: 0x4a90e2,
      transparent: true,
      opacity: 0.9,
      shininess: 30,
      specular: 0x111111
    });

    this.globe = new THREE.Mesh(geometry, material);
    this.globe.position.set(0, 0, 0);
    this.scene.add(this.globe);

    // Add atmosphere glow effect
    const atmosphereGeometry = new THREE.SphereGeometry(globeRadius * 1.05, 32, 32);
    const atmosphereMaterial = new THREE.MeshBasicMaterial({
      color: 0x0891b2,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });
    const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
    this.scene.add(atmosphere);

    // Add FEFU location marker (Vladivostok, Russia)
    this.createLocationMarker();

    // Add subtle wireframe for medical theme
    const wireframeGeometry = new THREE.SphereGeometry(globeRadius * 1.01, 16, 16);
    const wireframeMaterial = new THREE.MeshBasicMaterial({
      color: 0x2563eb,
      wireframe: true,
      transparent: true,
      opacity: 0.2
    });
    const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);
    this.scene.add(wireframe);
    this.wireframe = wireframe;
  }

  createLocationMarker() {
    // FEFU coordinates: approximately 43.0256°N, 131.8946°E
    // Convert to 3D sphere coordinates
    const lat = 43.0256 * (Math.PI / 180); // Convert to radians
    const lon = 131.8946 * (Math.PI / 180);
    const radius = 8.1; // Slightly above globe surface

    const x = radius * Math.cos(lat) * Math.cos(lon);
    const y = radius * Math.sin(lat);
    const z = radius * Math.cos(lat) * Math.sin(lon);

    // Create pulsing marker for FEFU location
    const markerGeometry = new THREE.SphereGeometry(0.3, 16, 16);
    const markerMaterial = new THREE.MeshBasicMaterial({
      color: 0xff4444,
      transparent: true,
      opacity: 0.9
    });

    const marker = new THREE.Mesh(markerGeometry, markerMaterial);
    marker.position.set(x, y, z);
    this.scene.add(marker);
    this.locationMarker = marker;

    // Add pulsing ring around marker
    const ringGeometry = new THREE.RingGeometry(0.4, 0.6, 16);
    const ringMaterial = new THREE.MeshBasicMaterial({
      color: 0xff4444,
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    });

    const ring = new THREE.Mesh(ringGeometry, ringMaterial);
    ring.position.copy(marker.position);
    ring.lookAt(0, 0, 0); // Face the center of the globe
    this.scene.add(ring);
    this.markerRing = ring;

    console.log('✅ FEFU location marker created at Vladivostok coordinates');
  }

  createEnhancedDNA() {
    console.log('Creating enhanced DNA helix model...');

    // Create DNA helix with improved visual quality
    this.createPremiumDNAHelix();
  }

  createPremiumDNAHelix() {
    const helixGroup = new THREE.Group();
    const helixHeight = 16;
    const helixRadius = 2.5;
    const segments = this.isMobile ? 60 : 120;
    const turns = 3; // Number of complete turns

    // Enhanced materials with better lighting response
    const strand1Material = new THREE.MeshPhongMaterial({
      color: 0x0891b2,
      shininess: 100,
      transparent: true,
      opacity: 0.9
    });

    const strand2Material = new THREE.MeshPhongMaterial({
      color: 0x2563eb,
      shininess: 100,
      transparent: true,
      opacity: 0.9
    });

    const basePairMaterial = new THREE.MeshPhongMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.7
    });

    // Create DNA strands with improved geometry
    for (let i = 0; i < segments; i++) {
      const progress = i / segments;
      const angle1 = progress * Math.PI * 2 * turns;
      const angle2 = angle1 + Math.PI;
      const y = progress * helixHeight - helixHeight / 2;

      // First strand with varying sizes
      const size1 = 0.15 + Math.sin(progress * Math.PI * 8) * 0.05;
      const sphere1 = new THREE.Mesh(
        new THREE.SphereGeometry(size1, 12, 12),
        strand1Material
      );
      sphere1.position.set(
        Math.cos(angle1) * helixRadius,
        y,
        Math.sin(angle1) * helixRadius
      );
      helixGroup.add(sphere1);

      // Second strand with varying sizes
      const size2 = 0.15 + Math.cos(progress * Math.PI * 8) * 0.05;
      const sphere2 = new THREE.Mesh(
        new THREE.SphereGeometry(size2, 12, 12),
        strand2Material
      );
      sphere2.position.set(
        Math.cos(angle2) * helixRadius,
        y,
        Math.sin(angle2) * helixRadius
      );
      helixGroup.add(sphere2);

      // Enhanced base pairs every few segments
      if (i % 4 === 0) {
        const basePairGeometry = new THREE.CylinderGeometry(0.05, 0.05, helixRadius * 2, 8);
        const basePair = new THREE.Mesh(basePairGeometry, basePairMaterial);
        basePair.position.set(0, y, 0);
        basePair.rotation.z = angle1;
        helixGroup.add(basePair);
      }
    }

    // Position DNA to the side of the globe
    helixGroup.position.set(-12, 0, 0);
    helixGroup.scale.set(1.2, 1.2, 1.2);
    this.scene.add(helixGroup);
    this.dnaHelix = helixGroup;

    console.log('✅ Enhanced DNA helix created with premium materials');
  }



  setupControls() {
    // Mouse interaction for camera
    let mouseX = 0;
    let mouseY = 0;
    
    document.addEventListener('mousemove', (event) => {
      mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
    });
    
    this.mouseX = mouseX;
    this.mouseY = mouseY;
  }

  animate() {
    this.animationId = requestAnimationFrame(() => this.animate());

    const time = Date.now() * 0.001;

    // Smooth globe rotation
    if (this.globe) {
      this.globe.rotation.y += 0.005; // Slower, more realistic rotation
      this.globe.rotation.x = Math.sin(time * 0.3) * 0.05; // Subtle wobble
    }

    // Animate wireframe
    if (this.wireframe) {
      this.wireframe.rotation.y += 0.008;
    }

    // Animate location marker with pulsing effect
    if (this.locationMarker) {
      const pulse = 1 + Math.sin(time * 3) * 0.2;
      this.locationMarker.scale.setScalar(pulse);
    }

    // Animate marker ring
    if (this.markerRing) {
      this.markerRing.rotation.z += 0.02;
      const ringPulse = 1 + Math.sin(time * 2) * 0.1;
      this.markerRing.scale.setScalar(ringPulse);
    }

    // Enhanced DNA helix animation
    if (this.dnaHelix) {
      this.dnaHelix.rotation.y += 0.01;
      this.dnaHelix.position.y = Math.sin(time * 0.5) * 1;

      // Add subtle scaling animation
      const scale = 1 + Math.sin(time * 0.8) * 0.05;
      this.dnaHelix.scale.set(1.2 * scale, 1.2, 1.2 * scale);
    }

    // Smooth camera movement based on mouse (only on desktop)
    if (!this.isMobile && this.mouseX !== undefined) {
      const targetX = this.mouseX * 3;
      const targetY = -this.mouseY * 3;

      this.camera.position.x += (targetX - this.camera.position.x) * 0.02;
      this.camera.position.y += (targetY - this.camera.position.y) * 0.02;
      this.camera.lookAt(0, 0, 0);
    }

    this.renderer.render(this.scene, this.camera);
  }

  setupResponsive() {
    window.addEventListener('resize', () => {
      const container = document.getElementById('globe-container');
      if (!container) return;

      const containerRect = container.getBoundingClientRect();
      const width = containerRect.width;
      const height = containerRect.height;

      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);

      // Update mobile status
      const wasMobile = this.isMobile;
      this.isMobile = window.innerWidth < 768;

      if (wasMobile !== this.isMobile) {
        // Recreate scene with appropriate quality
        this.dispose();
        setTimeout(() => this.init(), 100);
      }
    });
  }

  createFallbackVisuals() {
    console.log('Creating fallback visuals for globe container...');

    const container = document.getElementById('globe-container');
    if (!container) {
      console.error('Globe container not found for fallback');
      return;
    }

    container.innerHTML = `
      <div class="fallback-globe">
        <div class="globe-fallback">
          <div class="globe-sphere"></div>
          <div class="globe-marker"></div>
          <div class="globe-rings">
            <div class="ring ring-1"></div>
            <div class="ring ring-2"></div>
            <div class="ring ring-3"></div>
          </div>
        </div>
        <div class="fallback-dna">
          <div class="dna-strand strand-1"></div>
          <div class="dna-strand strand-2"></div>
        </div>
        <div class="fallback-text">
          <p>Interactive 3D Globe</p>
          <small>FEFU Location: Vladivostok, Russia</small>
        </div>
      </div>
    `;

    // Add CSS for fallback animations
    this.injectFallbackStyles();
    console.log('✅ Fallback visuals created successfully');
  }

  injectFallbackStyles() {
    if (document.getElementById('fallback-globe-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'fallback-globe-styles';
    styles.textContent = `
      .fallback-globe {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 100%);
        border-radius: 12px;
        overflow: hidden;
      }

      .globe-fallback {
        position: relative;
        width: 200px;
        height: 200px;
        margin-bottom: 1rem;
      }

      .globe-sphere {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%);
        border-radius: 50%;
        position: relative;
        animation: globeRotate 20s linear infinite;
        box-shadow: 0 0 30px rgba(37, 99, 235, 0.3);
      }

      .globe-marker {
        position: absolute;
        top: 30%;
        right: 25%;
        width: 12px;
        height: 12px;
        background: #ff4444;
        border-radius: 50%;
        animation: markerPulse 2s ease-in-out infinite;
        box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
      }

      .globe-rings {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .ring {
        position: absolute;
        border: 2px solid rgba(37, 99, 235, 0.3);
        border-radius: 50%;
        animation: ringRotate 15s linear infinite;
      }

      .ring-1 {
        width: 220px;
        height: 220px;
        top: -110px;
        left: -110px;
      }

      .ring-2 {
        width: 250px;
        height: 250px;
        top: -125px;
        left: -125px;
        animation-delay: -5s;
      }

      .ring-3 {
        width: 280px;
        height: 280px;
        top: -140px;
        left: -140px;
        animation-delay: -10s;
      }

      .fallback-dna {
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 120px;
      }

      .dna-strand {
        position: absolute;
        width: 4px;
        height: 100%;
        border-radius: 2px;
        animation: dnaFloat 3s ease-in-out infinite;
      }

      .strand-1 {
        left: 0;
        background: linear-gradient(to bottom, #0891b2, #2563eb);
      }

      .strand-2 {
        right: 0;
        background: linear-gradient(to bottom, #2563eb, #0891b2);
        animation-delay: -1.5s;
      }

      .fallback-text {
        text-align: center;
        color: white;
        margin-top: 1rem;
      }

      .fallback-text p {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .fallback-text small {
        opacity: 0.8;
        font-size: 0.9rem;
      }

      @keyframes globeRotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      @keyframes markerPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.3); opacity: 0.7; }
      }

      @keyframes ringRotate {
        from { transform: translate(-50%, -50%) rotate(0deg); }
        to { transform: translate(-50%, -50%) rotate(360deg); }
      }

      @keyframes dnaFloat {
        0%, 100% { transform: translateY(0) scaleY(1); }
        50% { transform: translateY(-10px) scaleY(1.1); }
      }
    `;
    document.head.appendChild(styles);
  }

  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.renderer) {
      this.renderer.dispose();
    }

    if (this.scene) {
      this.scene.clear();
    }
  }
}

// Initialize 3D components when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log('🚀 DOM loaded, initializing Medical 3D Components...');

  // Check if globe container exists
  const globeContainer = document.getElementById('globe-container');
  if (globeContainer) {
    console.log('✅ Globe container found, initializing 3D globe...');

    // Initialize 3D components
    try {
      new Medical3DComponents();
    } catch (error) {
      console.error('❌ Failed to initialize Medical3DComponents:', error);

      // Create fallback manually if initialization fails
      const fallbackComponent = new Medical3DComponents();
      fallbackComponent.createFallbackVisuals();
    }
  } else {
    console.warn('⚠️ Globe container not found in DOM');
  }
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Medical3DComponents;
}
