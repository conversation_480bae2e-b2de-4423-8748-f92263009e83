/* ===== MEDICAL 3D COMPONENTS ===== */
/* Advanced 3D visualizations for medical education website */

class Medical3DComponents {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.globe = null;
    this.dnaHelix = null;
    this.animationId = null;
    this.isMobile = window.innerWidth < 768;
    this.isWebGLSupported = this.checkWebGLSupport();
    this.init();
  }

  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  async init() {
    console.log('🚀 Initializing Medical 3D Components...');

    // Check if Three.js is available and WebGL is supported
    if (typeof THREE === 'undefined') {
      console.warn('Three.js not loaded, using fallback');
      this.createFallbackVisuals();
      return;
    }

    if (!this.isWebGLSupported) {
      console.warn('WebGL not supported, using fallback');
      this.createFallbackVisuals();
      return;
    }

    try {
      this.setupScene();
      this.createInteractiveGlobe();
      this.createEnhancedDNA();
      this.setupControls();
      this.animate();
      this.setupResponsive();
      console.log('✅ Medical 3D Components initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize 3D components:', error);
      this.createFallbackVisuals();
    }
  }

  setupScene() {
    // Create scene with medical theme
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x0a0a1a);
    this.scene.fog = new THREE.Fog(0x0a0a1a, 30, 100);

    // Create camera with proper aspect ratio for globe container
    const container = document.getElementById('globe-container');
    if (!container) {
      throw new Error('Globe container not found');
    }

    const containerRect = container.getBoundingClientRect();
    const aspect = containerRect.width / containerRect.height;
    this.camera = new THREE.PerspectiveCamera(60, aspect, 0.1, 1000);
    this.camera.position.set(0, 0, 25);

    // Create optimized renderer
    this.renderer = new THREE.WebGLRenderer({
      antialias: !this.isMobile,
      alpha: true,
      powerPreference: this.isMobile ? 'low-power' : 'high-performance'
    });

    this.renderer.setSize(containerRect.width, containerRect.height);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.isMobile ? 1 : 2));
    this.renderer.shadowMap.enabled = !this.isMobile;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.outputEncoding = THREE.sRGBEncoding;

    // Clear loading state and add renderer to globe container
    container.innerHTML = '';
    container.appendChild(this.renderer.domElement);

    // Setup lighting
    this.setupLighting();
  }

  setupLighting() {
    // Ambient light for overall illumination
    const ambientLight = new THREE.AmbientLight(0x2563eb, 0.4);
    this.scene.add(ambientLight);

    // Main directional light (sun-like)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    directionalLight.position.set(30, 30, 30);
    if (!this.isMobile) {
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 1024;
      directionalLight.shadow.mapSize.height = 1024;
      directionalLight.shadow.camera.near = 0.1;
      directionalLight.shadow.camera.far = 100;
    }
    this.scene.add(directionalLight);

    // Medical-themed accent lights
    const medicalLight1 = new THREE.PointLight(0x0891b2, 0.6, 50);
    medicalLight1.position.set(15, 10, 15);
    this.scene.add(medicalLight1);

    const medicalLight2 = new THREE.PointLight(0x2563eb, 0.4, 40);
    medicalLight2.position.set(-15, -10, 15);
    this.scene.add(medicalLight2);

    // Rim light for dramatic effect
    const rimLight = new THREE.DirectionalLight(0x0891b2, 0.3);
    rimLight.position.set(-30, 0, -30);
    this.scene.add(rimLight);
  }

  createMedicalGlobe() {
    // Create globe geometry
    const geometry = new THREE.SphereGeometry(8, this.isMobile ? 32 : 64, this.isMobile ? 32 : 64);
    
    // Create medical-themed material
    const material = new THREE.MeshPhongMaterial({
      color: 0x2563eb,
      transparent: true,
      opacity: 0.8,
      shininess: 100
    });

    this.globe = new THREE.Mesh(geometry, material);
    this.globe.position.set(15, 0, 0);
    this.scene.add(this.globe);

    // Add wireframe overlay
    const wireframeGeometry = new THREE.SphereGeometry(8.1, 16, 16);
    const wireframeMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ff88,
      wireframe: true,
      transparent: true,
      opacity: 0.3
    });
    const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);
    wireframe.position.set(15, 0, 0);
    this.scene.add(wireframe);

    // Add medical symbols
    this.createMedicalSymbols();
  }

  createMedicalSymbols() {
    const symbolGroup = new THREE.Group();
    
    // Create medical cross symbols
    for (let i = 0; i < (this.isMobile ? 8 : 12); i++) {
      const crossGeometry = new THREE.BoxGeometry(0.5, 2, 0.1);
      const crossMaterial = new THREE.MeshPhongMaterial({ color: 0xff4444 });
      const cross1 = new THREE.Mesh(crossGeometry, crossMaterial);
      
      const cross2 = new THREE.Mesh(crossGeometry, crossMaterial);
      cross2.rotation.z = Math.PI / 2;
      
      const crossGroup = new THREE.Group();
      crossGroup.add(cross1);
      crossGroup.add(cross2);
      
      // Position around globe
      const angle = (i / (this.isMobile ? 8 : 12)) * Math.PI * 2;
      crossGroup.position.set(
        Math.cos(angle) * 12,
        Math.sin(angle) * 12,
        Math.sin(angle * 2) * 3
      );
      
      symbolGroup.add(crossGroup);
    }
    
    symbolGroup.position.set(15, 0, 0);
    this.scene.add(symbolGroup);
    this.medicalSymbols = symbolGroup;
  }

  createFloatingElements() {
    this.floatingElements = new THREE.Group();
    
    // Create DNA helix
    this.createDNAHelix();
    
    // Create floating medical equipment
    this.createMedicalEquipment();
    
    this.scene.add(this.floatingElements);
  }

  createDNAHelix() {
    const helixGroup = new THREE.Group();
    const helixHeight = 20;
    const helixRadius = 3;
    const segments = this.isMobile ? 50 : 100;
    
    for (let i = 0; i < segments; i++) {
      const angle1 = (i / segments) * Math.PI * 4;
      const angle2 = angle1 + Math.PI;
      const y = (i / segments) * helixHeight - helixHeight / 2;
      
      // First strand
      const sphere1 = new THREE.Mesh(
        new THREE.SphereGeometry(0.2, 8, 8),
        new THREE.MeshPhongMaterial({ color: 0x00ff88 })
      );
      sphere1.position.set(
        Math.cos(angle1) * helixRadius,
        y,
        Math.sin(angle1) * helixRadius
      );
      helixGroup.add(sphere1);
      
      // Second strand
      const sphere2 = new THREE.Mesh(
        new THREE.SphereGeometry(0.2, 8, 8),
        new THREE.MeshPhongMaterial({ color: 0xff8800 })
      );
      sphere2.position.set(
        Math.cos(angle2) * helixRadius,
        y,
        Math.sin(angle2) * helixRadius
      );
      helixGroup.add(sphere2);
      
      // Connecting lines
      if (i % 5 === 0) {
        const lineGeometry = new THREE.BufferGeometry().setFromPoints([
          sphere1.position,
          sphere2.position
        ]);
        const lineMaterial = new THREE.LineBasicMaterial({ 
          color: 0xffffff,
          transparent: true,
          opacity: 0.5
        });
        const line = new THREE.Line(lineGeometry, lineMaterial);
        helixGroup.add(line);
      }
    }
    
    helixGroup.position.set(-15, 0, 0);
    this.floatingElements.add(helixGroup);
    this.dnaHelix = helixGroup;
  }

  createMedicalEquipment() {
    // Create stethoscope representation
    const stethoscopeGroup = new THREE.Group();
    
    // Stethoscope head
    const headGeometry = new THREE.CylinderGeometry(1, 1, 0.5, 16);
    const headMaterial = new THREE.MeshPhongMaterial({ color: 0x888888 });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.set(0, 5, 0);
    stethoscopeGroup.add(head);
    
    // Stethoscope tube (simplified)
    const tubeGeometry = new THREE.TorusGeometry(3, 0.2, 8, 16);
    const tubeMaterial = new THREE.MeshPhongMaterial({ color: 0x333333 });
    const tube = new THREE.Mesh(tubeGeometry, tubeMaterial);
    tube.position.set(0, 2, 0);
    stethoscopeGroup.add(tube);
    
    stethoscopeGroup.position.set(0, 10, -10);
    stethoscopeGroup.scale.set(0.5, 0.5, 0.5);
    this.floatingElements.add(stethoscopeGroup);
    this.stethoscope = stethoscopeGroup;
  }

  setupControls() {
    // Mouse interaction for camera
    let mouseX = 0;
    let mouseY = 0;
    
    document.addEventListener('mousemove', (event) => {
      mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
    });
    
    this.mouseX = mouseX;
    this.mouseY = mouseY;
  }

  animate() {
    this.animationId = requestAnimationFrame(() => this.animate());
    
    const time = Date.now() * 0.001;
    
    // Rotate globe
    if (this.globe) {
      this.globe.rotation.y += 0.01;
      this.globe.rotation.x = Math.sin(time * 0.5) * 0.1;
    }
    
    // Rotate medical symbols
    if (this.medicalSymbols) {
      this.medicalSymbols.rotation.y += 0.02;
    }
    
    // Animate DNA helix
    if (this.dnaHelix) {
      this.dnaHelix.rotation.y += 0.015;
      this.dnaHelix.position.y = Math.sin(time) * 2;
    }
    
    // Animate stethoscope
    if (this.stethoscope) {
      this.stethoscope.rotation.z = Math.sin(time * 2) * 0.1;
      this.stethoscope.position.y = 10 + Math.sin(time * 1.5) * 1;
    }
    
    // Camera movement based on mouse
    if (!this.isMobile) {
      this.camera.position.x += (this.mouseX * 5 - this.camera.position.x) * 0.05;
      this.camera.position.y += (-this.mouseY * 5 - this.camera.position.y) * 0.05;
      this.camera.lookAt(this.scene.position);
    }
    
    this.renderer.render(this.scene, this.camera);
  }

  setupResponsive() {
    window.addEventListener('resize', () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);
      
      // Update mobile status
      const wasMobile = this.isMobile;
      this.isMobile = width < 768;
      
      if (wasMobile !== this.isMobile) {
        // Recreate scene with appropriate quality
        this.dispose();
        this.init();
      }
    });
  }

  createFallbackVisuals() {
    // CSS-only fallback for when Three.js isn't available
    const container = document.getElementById('hero-3d-background');
    if (!container) return;
    
    container.innerHTML = `
      <div class="fallback-3d">
        <div class="floating-element morphing-shape" style="background: var(--gradient-medical); width: 200px; height: 200px; position: absolute; top: 20%; right: 10%;"></div>
        <div class="floating-element" style="background: var(--gradient-success); width: 100px; height: 100px; border-radius: 50%; position: absolute; bottom: 30%; left: 15%; animation-delay: 2s;"></div>
        <div class="floating-element" style="background: var(--gradient-premium); width: 150px; height: 150px; border-radius: 30%; position: absolute; top: 60%; right: 20%; animation-delay: 4s;"></div>
      </div>
    `;
  }

  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    if (this.scene) {
      this.scene.clear();
    }
  }
}

// Initialize 3D components
document.addEventListener('DOMContentLoaded', () => {
  // Add 3D background container to hero section
  const heroSection = document.querySelector('.hero-section');
  if (heroSection) {
    const bg3D = document.createElement('div');
    bg3D.id = 'hero-3d-background';
    bg3D.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      overflow: hidden;
    `;
    heroSection.appendChild(bg3D);
    
    // Initialize 3D components
    new Medical3DComponents();
  }
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Medical3DComponents;
}
