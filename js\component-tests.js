/* ===== COMPONENT TESTING SUITE ===== */
/* Comprehensive testing for all website components */

class ComponentTester {
  constructor() {
    this.testResults = [];
    this.init();
  }

  init() {
    console.log('🧪 Starting component testing suite...');
    this.runAllTests();
  }

  async runAllTests() {
    await this.test3DComponents();
    await this.testLoadingStates();
    await this.testInteractiveElements();
    await this.testPerformance();
    await this.testAccessibility();
    this.generateTestReport();
  }

  async test3DComponents() {
    console.log('🎮 Testing 3D components...');
    
    // Test Three.js availability
    this.addTestResult('Three.js Library', typeof THREE !== 'undefined', 'Three.js should be loaded');
    
    // Test WebGL support
    const webglSupported = this.checkWebGLSupport();
    this.addTestResult('WebGL Support', webglSupported, 'WebGL should be supported');
    
    // Test globe container
    const globeContainer = document.getElementById('globe-container');
    this.addTestResult('Globe Container', !!globeContainer, 'Globe container should exist');
    
    if (globeContainer) {
      // Wait for 3D initialization
      await this.waitFor(3000);
      
      const hasCanvas = globeContainer.querySelector('canvas');
      const hasFallback = globeContainer.querySelector('.fallback-globe, .css-globe-fallback');
      
      this.addTestResult('Globe Content', hasCanvas || hasFallback, 'Globe should have canvas or fallback content');
      
      if (hasCanvas) {
        this.addTestResult('3D Globe Rendering', true, '3D globe is rendering');
      } else if (hasFallback) {
        this.addTestResult('Fallback Globe', true, 'Fallback globe is displayed');
      } else {
        this.addTestResult('Globe Implementation', false, 'No globe content found');
      }
    }
  }

  async testLoadingStates() {
    console.log('⏳ Testing loading states...');

    // Check for stuck loading states
    const loadingElements = document.querySelectorAll('.component-loading');
    this.addTestResult('Loading States Cleared', loadingElements.length === 0, 'All loading states should be cleared');

    // Test component containers
    const containers = [
      'globe-container',
      'info-cards-container',
      'campus-gallery'
    ];

    containers.forEach(containerId => {
      const container = document.getElementById(containerId);
      if (container) {
        const hasContent = container.children.length > 0 && !container.querySelector('.component-loading');
        this.addTestResult(`${containerId} Content`, hasContent, `${containerId} should have content`);
      }
    });

    // Test stagger items visibility
    await this.testStaggerItemsVisibility();
  }

  async testStaggerItemsVisibility() {
    console.log('🎭 Testing stagger items visibility...');

    const staggerSections = [
      { name: 'Country Cards', selector: '.country-card.stagger-item', expectedCount: 4 },
      { name: 'Facility Cards', selector: '.facility-card.stagger-item', expectedCount: 4 },
      { name: 'Safety Cards', selector: '.safety-card.stagger-item', expectedCount: 6 },
      { name: 'About Cards', selector: '.about-card.stagger-item', expectedCount: 3 },
      { name: 'Year Cards', selector: '.year-card.stagger-item', expectedCount: 6 },
      { name: 'Hospital Cards', selector: '.hospital-card.stagger-item', expectedCount: 3 }
    ];

    staggerSections.forEach(section => {
      const items = document.querySelectorAll(section.selector);

      // Test count
      this.addTestResult(
        `${section.name} Count`,
        items.length === section.expectedCount,
        `Found ${items.length}/${section.expectedCount} ${section.name.toLowerCase()}`
      );

      // Test visibility
      let visibleItems = 0;
      items.forEach(item => {
        const styles = getComputedStyle(item);
        if (styles.opacity !== '0') {
          visibleItems++;
        }
      });

      this.addTestResult(
        `${section.name} Visibility`,
        visibleItems > 0,
        `${visibleItems}/${items.length} ${section.name.toLowerCase()} are visible`
      );
    });

    // Test for orphaned stagger items
    const allStaggerItems = document.querySelectorAll('.stagger-item');
    const orphanedItems = [];

    allStaggerItems.forEach(item => {
      let hasRevealParent = false;
      let currentElement = item.parentElement;

      while (currentElement && currentElement !== document.body) {
        if (currentElement.classList.contains('reveal-up') ||
            currentElement.classList.contains('reveal-left') ||
            currentElement.classList.contains('reveal-right') ||
            currentElement.classList.contains('reveal-scale')) {
          hasRevealParent = true;
          break;
        }
        currentElement = currentElement.parentElement;
      }

      if (!hasRevealParent) {
        orphanedItems.push(item);
      }
    });

    this.addTestResult(
      'Stagger Items Triggers',
      orphanedItems.length === 0,
      orphanedItems.length === 0 ? 'All stagger items have reveal triggers' : `${orphanedItems.length} orphaned stagger items found`
    );
  }

  async testInteractiveElements() {
    console.log('🖱️ Testing interactive elements...');
    
    // Test navigation
    const navToggle = document.querySelector('.nav-toggle');
    this.addTestResult('Navigation Toggle', !!navToggle, 'Navigation toggle should exist');
    
    // Test buttons
    const buttons = document.querySelectorAll('.premium-button, .cta-button');
    this.addTestResult('Interactive Buttons', buttons.length > 0, 'Interactive buttons should exist');
    
    // Test magnetic hover elements
    const magneticElements = document.querySelectorAll('.magnetic-hover');
    this.addTestResult('Magnetic Hover Elements', magneticElements.length > 0, 'Magnetic hover elements should exist');
    
    // Test form elements
    const formInputs = document.querySelectorAll('input, textarea, select');
    this.addTestResult('Form Elements', formInputs.length > 0, 'Form elements should exist');
  }

  async testPerformance() {
    console.log('⚡ Testing performance...');
    
    // Test animation performance
    const animatedElements = document.querySelectorAll('.floating-element, .floating-medical');
    this.addTestResult('Animated Elements', animatedElements.length > 0, 'Animated elements should exist');
    
    // Test hardware acceleration
    const acceleratedElements = document.querySelectorAll('.premium-card, .info-card');
    let hardwareAccelerated = 0;
    
    acceleratedElements.forEach(el => {
      const style = getComputedStyle(el);
      if (style.willChange !== 'auto' || style.transform !== 'none') {
        hardwareAccelerated++;
      }
    });
    
    this.addTestResult('Hardware Acceleration', hardwareAccelerated > 0, 'Elements should use hardware acceleration');
    
    // Test Core Web Vitals readiness
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
      const style = getComputedStyle(heroSection);
      const hasContainment = style.contain !== 'none';
      this.addTestResult('Layout Containment', hasContainment, 'Hero section should use containment');
    }
  }

  async testAccessibility() {
    console.log('♿ Testing accessibility...');
    
    // Test ARIA labels
    const ariaElements = document.querySelectorAll('[aria-label], [aria-labelledby], [role]');
    this.addTestResult('ARIA Attributes', ariaElements.length > 0, 'Elements should have ARIA attributes');
    
    // Test keyboard navigation
    const focusableElements = document.querySelectorAll('a, button, input, textarea, select, [tabindex]');
    this.addTestResult('Focusable Elements', focusableElements.length > 0, 'Focusable elements should exist');
    
    // Test alt text on images
    const images = document.querySelectorAll('img');
    let imagesWithAlt = 0;
    images.forEach(img => {
      if (img.alt && img.alt.trim() !== '') {
        imagesWithAlt++;
      }
    });
    
    this.addTestResult('Image Alt Text', imagesWithAlt === images.length, 'All images should have alt text');
    
    // Test color contrast (basic check)
    const textElements = document.querySelectorAll('h1, h2, h3, p, span, a');
    this.addTestResult('Text Elements', textElements.length > 0, 'Text elements should exist');
  }

  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  addTestResult(testName, passed, description) {
    this.testResults.push({
      name: testName,
      passed,
      description,
      timestamp: new Date().toISOString()
    });
  }

  async waitFor(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateTestReport() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.group('🧪 Component Testing Report');
    console.log(`Overall: ${passedTests}/${totalTests} tests passed (${passRate}%)`);
    
    const failedTests = this.testResults.filter(test => !test.passed);
    if (failedTests.length > 0) {
      console.group('❌ Failed Tests:');
      failedTests.forEach(test => {
        console.error(`${test.name}: ${test.description}`);
      });
      console.groupEnd();
    }
    
    const passedTestsList = this.testResults.filter(test => test.passed);
    if (passedTestsList.length > 0) {
      console.group('✅ Passed Tests:');
      passedTestsList.forEach(test => {
        console.log(`${test.name}: ${test.description}`);
      });
      console.groupEnd();
    }
    
    console.groupEnd();
    
    // Store results globally for debugging
    window.testResults = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        passRate: passRate + '%'
      },
      details: this.testResults
    };
    
    // Show visual indicator
    this.showTestIndicator(passRate);
    
    return this.testResults;
  }

  showTestIndicator(passRate) {
    // Create a visual test indicator
    const indicator = document.createElement('div');
    indicator.id = 'test-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 50px;
      right: 10px;
      z-index: 10000;
      padding: 10px 15px;
      border-radius: 8px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      ${passRate >= 90 ? 'background: #28a745;' : passRate >= 70 ? 'background: #ffc107; color: #000;' : 'background: #dc3545;'}
    `;
    
    indicator.innerHTML = `
      <div>Tests: ${passRate}%</div>
      <div style="font-size: 10px; opacity: 0.8;">Click for details</div>
    `;
    
    indicator.addEventListener('click', () => {
      console.log('Test Results:', window.testResults);
    });
    
    document.body.appendChild(indicator);
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => indicator.remove(), 300);
      }
    }, 10000);
  }
}

// Auto-run tests when DOM is ready and components are initialized
document.addEventListener('DOMContentLoaded', () => {
  // Wait for components to initialize
  setTimeout(() => {
    new ComponentTester();
  }, 5000);
});

// Manual test trigger
window.runComponentTests = function() {
  new ComponentTester();
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ComponentTester;
}
