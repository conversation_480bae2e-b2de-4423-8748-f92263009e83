/* ===== PREMIUM NAVIGATION SYSTEM ===== */
/* Advanced navigation with smooth scrolling, mobile optimization, and premium features */

// Prevent class redeclaration
if (typeof window.PremiumNavigation !== 'undefined') {
  console.warn('PremiumNavigation already exists, skipping redeclaration');
} else {

class PremiumNavigation {
  constructor() {
    this.navbar = null;
    this.mobileOverlay = null;
    this.searchOverlay = null;
    this.navToggle = null;
    this.currentSection = 'home';
    this.isScrolling = false;
    this.lastScrollY = 0;
    this.scrollDirection = 'up';
    
    this.init();
  }

  init() {
    console.log('🏥 Initializing Premium Navigation System...');
    
    this.setupElements();
    this.setupEventListeners();
    this.setupScrollBehavior();
    this.setupActiveNavigation();
    this.setupSearchFunctionality();
    this.setupLanguageSelector();
    this.setupAccessibilityFeatures();
    this.updateDNANavigationPosition();
    
    console.log('✅ Premium Navigation initialized successfully');
  }

  setupElements() {
    this.navbar = document.getElementById('premium-navbar');
    this.mobileOverlay = document.getElementById('mobile-nav-overlay');
    this.searchOverlay = document.getElementById('search-overlay');
    this.navToggle = document.getElementById('nav-toggle');
    this.breadcrumbContainer = document.getElementById('breadcrumb-container');
    
    if (!this.navbar) {
      console.error('Premium navbar not found');
      return;
    }
  }

  setupEventListeners() {
    // Mobile menu toggle
    if (this.navToggle) {
      this.navToggle.addEventListener('click', () => {
        this.toggleMobileMenu();
      });
    }

    // Mobile menu close
    const mobileClose = document.getElementById('mobile-nav-close');
    if (mobileClose) {
      mobileClose.addEventListener('click', () => {
        this.closeMobileMenu();
      });
    }

    // Search functionality
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => {
        this.openSearch();
      });
    }

    const searchClose = document.getElementById('search-close');
    if (searchClose) {
      searchClose.addEventListener('click', () => {
        this.closeSearch();
      });
    }

    // Navigation links
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const section = link.getAttribute('data-section') || link.getAttribute('href').substring(1);
        this.navigateToSection(section);
        this.closeMobileMenu();
      });
    });

    // Search suggestions
    const searchSuggestions = document.querySelectorAll('.search-suggestion');
    searchSuggestions.forEach(suggestion => {
      suggestion.addEventListener('click', (e) => {
        e.preventDefault();
        const section = suggestion.getAttribute('href').substring(1);
        this.navigateToSection(section);
        this.closeSearch();
      });
    });

    // Overlay clicks
    if (this.mobileOverlay) {
      this.mobileOverlay.addEventListener('click', (e) => {
        if (e.target === this.mobileOverlay) {
          this.closeMobileMenu();
        }
      });
    }

    if (this.searchOverlay) {
      this.searchOverlay.addEventListener('click', (e) => {
        if (e.target === this.searchOverlay) {
          this.closeSearch();
        }
      });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // Window resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  setupScrollBehavior() {
    let ticking = false;

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    });
  }

  handleScroll() {
    const currentScrollY = window.pageYOffset;
    const scrollDifference = currentScrollY - this.lastScrollY;
    
    // Determine scroll direction
    if (scrollDifference > 0 && currentScrollY > 100) {
      this.scrollDirection = 'down';
    } else {
      this.scrollDirection = 'up';
    }

    // Add scrolled class for styling
    if (currentScrollY > 50) {
      this.navbar.classList.add('scrolled');
    } else {
      this.navbar.classList.remove('scrolled');
    }

    // Hide/show navbar on scroll (optional)
    if (currentScrollY > 200) {
      if (this.scrollDirection === 'down' && !this.isScrolling) {
        this.navbar.classList.add('hidden');
      } else if (this.scrollDirection === 'up') {
        this.navbar.classList.remove('hidden');
      }
    } else {
      this.navbar.classList.remove('hidden');
    }

    this.lastScrollY = currentScrollY;
    this.updateActiveNavigation();
  }

  setupActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const observerOptions = {
      threshold: 0.3,
      rootMargin: '-80px 0px -80px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.setActiveSection(entry.target.id);
        }
      });
    }, observerOptions);

    sections.forEach(section => {
      observer.observe(section);
    });
  }

  updateActiveNavigation() {
    // This method is called during scroll to update active states
    // The intersection observer handles the main logic
  }

  setActiveSection(sectionId) {
    if (this.currentSection === sectionId) return;
    
    this.currentSection = sectionId;
    
    // Update navigation links
    const allNavLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
    allNavLinks.forEach(link => {
      const linkSection = link.getAttribute('data-section');
      if (linkSection === sectionId) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });

    // Update breadcrumb
    this.updateBreadcrumb(sectionId);
    
    // Integrate with DNA navigation
    if (window.dnaNavigation) {
      const sectionIndex = window.dnaNavigation.sections.findIndex(section => section.id === sectionId);
      if (sectionIndex !== -1) {
        window.dnaNavigation.setCurrentSection(sectionIndex);
      }
    }
  }

  navigateToSection(sectionId) {
    const targetElement = document.getElementById(sectionId);
    if (!targetElement) {
      console.warn(`Section ${sectionId} not found`);
      return;
    }

    this.isScrolling = true;
    
    // Calculate offset for fixed navbar
    const navbarHeight = this.navbar.offsetHeight;
    const targetPosition = targetElement.offsetTop - navbarHeight - 20;

    // Smooth scroll
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });

    // Reset scrolling flag after animation
    setTimeout(() => {
      this.isScrolling = false;
    }, 1000);

    // Update active section immediately
    this.setActiveSection(sectionId);
    
    console.log(`🏥 Navigated to section: ${sectionId}`);
  }

  toggleMobileMenu() {
    if (this.mobileOverlay.classList.contains('active')) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  openMobileMenu() {
    this.mobileOverlay.classList.add('active');
    this.navToggle.classList.add('active');
    this.navToggle.setAttribute('aria-expanded', 'true');
    document.body.style.overflow = 'hidden';
    
    // Focus management
    const firstLink = this.mobileOverlay.querySelector('.mobile-nav-link');
    if (firstLink) {
      setTimeout(() => firstLink.focus(), 300);
    }
  }

  closeMobileMenu() {
    this.mobileOverlay.classList.remove('active');
    this.navToggle.classList.remove('active');
    this.navToggle.setAttribute('aria-expanded', 'false');
    document.body.style.overflow = '';
    
    // Return focus to toggle button
    this.navToggle.focus();
  }

  openSearch() {
    this.searchOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Focus on search input
    const searchInput = this.searchOverlay.querySelector('.search-input');
    if (searchInput) {
      setTimeout(() => searchInput.focus(), 300);
    }
  }

  closeSearch() {
    this.searchOverlay.classList.remove('active');
    document.body.style.overflow = '';
    
    // Return focus to search button
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
      searchBtn.focus();
    }
  }

  setupSearchFunctionality() {
    const searchInput = document.querySelector('.search-input');
    const searchSubmit = document.querySelector('.search-submit');
    
    if (searchInput && searchSubmit) {
      const handleSearch = () => {
        const query = searchInput.value.trim();
        if (query) {
          this.performSearch(query);
        }
      };

      searchSubmit.addEventListener('click', handleSearch);
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          handleSearch();
        }
      });
    }
  }

  performSearch(query) {
    console.log(`🔍 Searching for: ${query}`);
    
    // Simple search implementation - can be enhanced
    const searchTerms = {
      'curriculum': 'curriculum',
      'admission': 'apply',
      'campus': 'campus',
      'about': 'about',
      'career': 'career-paths',
      'fee': 'apply',
      'cost': 'apply'
    };

    const lowerQuery = query.toLowerCase();
    for (const [term, section] of Object.entries(searchTerms)) {
      if (lowerQuery.includes(term)) {
        this.navigateToSection(section);
        this.closeSearch();
        return;
      }
    }
    
    // If no match found, show all results or navigate to home
    this.navigateToSection('home');
    this.closeSearch();
  }

  setupLanguageSelector() {
    const langOptions = document.querySelectorAll('.lang-option');
    
    langOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        e.preventDefault();
        const lang = option.getAttribute('data-lang');
        this.changeLanguage(lang);
      });
    });
  }

  changeLanguage(lang) {
    console.log(`🌐 Changing language to: ${lang}`);
    
    // Update active language
    const langOptions = document.querySelectorAll('.lang-option');
    langOptions.forEach(option => {
      if (option.getAttribute('data-lang') === lang) {
        option.classList.add('active');
      } else {
        option.classList.remove('active');
      }
    });
    
    // Update language button text
    const langText = document.querySelector('.lang-text');
    if (langText) {
      langText.textContent = lang.toUpperCase();
    }
    
    // Here you would implement actual language switching logic
    // For now, just log the change
  }

  setupAccessibilityFeatures() {
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: var(--primary-600);
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 10000;
      transition: top 0.3s;
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  updateBreadcrumb(sectionId) {
    if (!this.breadcrumbContainer) return;
    
    const sectionNames = {
      'home': 'Home',
      'about': 'About FEFU',
      'curriculum': 'Curriculum',
      'campus': 'Campus Life',
      'career-paths': 'Career Paths',
      'apply': 'Apply Now'
    };
    
    const breadcrumb = this.breadcrumbContainer.querySelector('.breadcrumb');
    if (breadcrumb && sectionId !== 'home') {
      const activeItem = breadcrumb.querySelector('.breadcrumb-item.active');
      if (activeItem) {
        activeItem.textContent = sectionNames[sectionId] || sectionId;
      }
      this.breadcrumbContainer.style.display = 'block';
    } else {
      this.breadcrumbContainer.style.display = 'none';
    }
  }

  handleKeyboardShortcuts(e) {
    // Escape key closes overlays
    if (e.key === 'Escape') {
      if (this.mobileOverlay.classList.contains('active')) {
        this.closeMobileMenu();
      }
      if (this.searchOverlay.classList.contains('active')) {
        this.closeSearch();
      }
    }
    
    // Ctrl/Cmd + K opens search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      this.openSearch();
    }
  }

  handleResize() {
    // Close mobile menu on resize to desktop
    if (window.innerWidth > 768 && this.mobileOverlay.classList.contains('active')) {
      this.closeMobileMenu();
    }
    
    // Update DNA navigation position
    this.updateDNANavigationPosition();
  }

  updateDNANavigationPosition() {
    // Ensure DNA navigation doesn't conflict with premium navbar
    const dnaContainer = document.getElementById('dna-navigation');
    const dnaToggle = document.getElementById('dna-nav-toggle');
    
    if (dnaContainer) {
      const navbarHeight = this.navbar ? this.navbar.offsetHeight : 60;
      dnaContainer.style.top = `${navbarHeight + 20}px`;
    }
    
    if (dnaToggle) {
      const navbarHeight = this.navbar ? this.navbar.offsetHeight : 60;
      dnaToggle.style.top = `${navbarHeight + 60}px`;
    }
  }

  // Public methods for external control
  showQuickInfoBar() {
    const quickInfoBar = document.getElementById('quick-info-bar');
    if (quickInfoBar) {
      quickInfoBar.classList.add('visible');
    }
  }

  hideQuickInfoBar() {
    const quickInfoBar = document.getElementById('quick-info-bar');
    if (quickInfoBar) {
      quickInfoBar.classList.remove('visible');
    }
  }

  getCurrentSection() {
    return this.currentSection;
  }

  dispose() {
    // Cleanup event listeners and restore body overflow
    document.body.style.overflow = '';
    
    // Remove skip link
    const skipLink = document.querySelector('.skip-link');
    if (skipLink) {
      skipLink.remove();
    }
  }
}

// Register class globally and close protection block
window.PremiumNavigation = PremiumNavigation;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for other components to initialize
  setTimeout(() => {
    console.log('🏥 Initializing Premium Navigation...');
    window.premiumNavigation = new PremiumNavigation();
  }, 1000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PremiumNavigation;
}

} // Close class protection block
