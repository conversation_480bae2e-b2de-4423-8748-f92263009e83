/* ===== DNA NAVIGATION SYSTEM ===== */
/* Sophisticated scroll-responsive DNA helix navigation component */

class DNANavigation {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.dnaHelix = null;
    this.segments = [];
    this.animationId = null;
    this.isWebGLSupported = this.checkWebGLSupport();
    this.isMobile = window.innerWidth < 768;
    this.isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
    this.currentSection = 0;
    this.scrollProgress = 0;
    
    // Section configuration
    this.sections = [
      { id: 'career-paths', name: 'Career Pathways', color: '#2563eb', completed: false },
      { id: 'campus', name: 'Campus Life', color: '#0891b2', completed: false },
      { id: 'testimonials', name: 'Testimonials', color: '#059669', completed: false },
      { id: 'safety-support', name: 'Safety & Support', color: '#dc2626', completed: false },
      { id: 'about', name: 'About FEFU', color: '#7c3aed', completed: false },
      { id: 'curriculum', name: 'Curriculum', color: '#ea580c', completed: false }
    ];

    this.init();
  }

  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      return !!gl;
    } catch (e) {
      return false;
    }
  }

  async init() {
    console.log('🧬 Initializing DNA Navigation System...');
    
    // Create navigation container
    this.createNavigationContainer();
    
    if (typeof THREE === 'undefined' || !this.isWebGLSupported) {
      console.warn('WebGL not supported, using CSS fallback for DNA navigation');
      this.createCSSFallback();
      return;
    }

    try {
      this.setupScene();
      this.createDNAHelix();
      this.setupInteractions();
      this.setupScrollTracking();
      this.animate();
      this.setupResponsive();
      console.log('✅ DNA Navigation initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize DNA Navigation:', error);
      this.createCSSFallback();
    }
  }

  createNavigationContainer() {
    // Remove existing container if present
    const existing = document.getElementById('dna-navigation');
    if (existing) existing.remove();

    const container = document.createElement('div');
    container.id = 'dna-navigation';
    container.className = 'dna-navigation-container';
    
    // Responsive positioning
    const containerStyles = this.getContainerStyles();
    container.style.cssText = containerStyles;
    
    document.body.appendChild(container);
    this.container = container;
  }

  getContainerStyles() {
    if (this.isMobile) {
      return `
        position: fixed;
        top: 60px;
        right: 10px;
        width: 60px;
        height: 200px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 30px;
        transition: all 0.3s ease;
        transform: translateX(70px);
      `;
    } else if (this.isTablet) {
      return `
        position: fixed;
        top: 50%;
        right: 20px;
        width: 80px;
        height: 250px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 40px;
        transform: translateY(-50%);
        transition: all 0.3s ease;
      `;
    } else {
      return `
        position: fixed;
        top: 50%;
        right: 30px;
        width: 100px;
        height: 300px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        transform: translateY(-50%);
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
      `;
    }
  }

  setupScene() {
    const containerRect = this.container.getBoundingClientRect();
    
    // Create scene
    this.scene = new THREE.Scene();
    
    // Create camera
    const aspect = containerRect.width / containerRect.height;
    this.camera = new THREE.PerspectiveCamera(50, aspect, 0.1, 100);
    this.camera.position.set(0, 0, 15);
    
    // Create renderer
    this.renderer = new THREE.WebGLRenderer({ 
      alpha: true, 
      antialias: !this.isMobile,
      powerPreference: this.isMobile ? 'low-power' : 'high-performance'
    });
    
    this.renderer.setSize(containerRect.width, containerRect.height);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, this.isMobile ? 1 : 2));
    this.renderer.setClearColor(0x000000, 0);
    
    this.container.appendChild(this.renderer.domElement);
    
    // Setup lighting
    this.setupLighting();
  }

  setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x2563eb, 0.4);
    this.scene.add(ambientLight);
    
    // Directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    this.scene.add(directionalLight);
    
    // Point lights for medical theme
    const pointLight1 = new THREE.PointLight(0x0891b2, 0.6, 20);
    pointLight1.position.set(3, 3, 3);
    this.scene.add(pointLight1);
    
    const pointLight2 = new THREE.PointLight(0x2563eb, 0.4, 15);
    pointLight2.position.set(-3, -3, 3);
    this.scene.add(pointLight2);
  }

  createDNAHelix() {
    console.log('🧬 Creating DNA helix navigation structure...');
    
    this.dnaHelix = new THREE.Group();
    this.segments = [];
    
    const helixHeight = this.isMobile ? 8 : this.isTablet ? 10 : 12;
    const helixRadius = this.isMobile ? 1.5 : this.isTablet ? 1.8 : 2;
    const segmentCount = this.sections.length;
    const segmentHeight = helixHeight / segmentCount;
    
    this.sections.forEach((section, index) => {
      const segment = this.createDNASegment(section, index, segmentHeight, helixRadius, helixHeight);
      this.segments.push(segment);
      this.dnaHelix.add(segment.group);
    });
    
    this.scene.add(this.dnaHelix);
    this.dnaHelix.position.y = 0;
    
    console.log(`✅ Created ${segmentCount} DNA segments`);
  }

  createDNASegment(section, index, segmentHeight, helixRadius, totalHeight) {
    const segmentGroup = new THREE.Group();
    const yPosition = (index * segmentHeight) - (totalHeight / 2) + (segmentHeight / 2);
    
    // Create base pair strands
    const turns = 0.5; // Half turn per segment
    const strandPoints = 8; // Points per segment
    const strand1Points = [];
    const strand2Points = [];
    
    for (let i = 0; i <= strandPoints; i++) {
      const progress = i / strandPoints;
      const angle1 = progress * Math.PI * 2 * turns;
      const angle2 = angle1 + Math.PI;
      const y = yPosition + (progress - 0.5) * segmentHeight;
      
      strand1Points.push(new THREE.Vector3(
        Math.cos(angle1) * helixRadius,
        y,
        Math.sin(angle1) * helixRadius
      ));
      
      strand2Points.push(new THREE.Vector3(
        Math.cos(angle2) * helixRadius,
        y,
        Math.sin(angle2) * helixRadius
      ));
    }
    
    // Create strand geometries
    const strand1Geometry = new THREE.TubeGeometry(
      new THREE.CatmullRomCurve3(strand1Points),
      16, 0.1, 8, false
    );
    
    const strand2Geometry = new THREE.TubeGeometry(
      new THREE.CatmullRomCurve3(strand2Points),
      16, 0.1, 8, false
    );
    
    // Create materials
    const strand1Material = new THREE.MeshPhongMaterial({
      color: section.color,
      transparent: true,
      opacity: 0.3, // Start transparent
      shininess: 100
    });
    
    const strand2Material = new THREE.MeshPhongMaterial({
      color: section.color,
      transparent: true,
      opacity: 0.3, // Start transparent
      shininess: 100
    });
    
    // Create strand meshes
    const strand1Mesh = new THREE.Mesh(strand1Geometry, strand1Material);
    const strand2Mesh = new THREE.Mesh(strand2Geometry, strand2Material);
    
    segmentGroup.add(strand1Mesh);
    segmentGroup.add(strand2Mesh);
    
    // Create connecting base pairs
    const basePairCount = 3;
    const basePairs = [];
    
    for (let i = 0; i < basePairCount; i++) {
      const progress = (i + 1) / (basePairCount + 1);
      const angle = progress * Math.PI * 2 * turns;
      const y = yPosition + (progress - 0.5) * segmentHeight;
      
      const basePairGeometry = new THREE.CylinderGeometry(0.03, 0.03, helixRadius * 2, 6);
      const basePairMaterial = new THREE.MeshPhongMaterial({
        color: section.color,
        transparent: true,
        opacity: 0.2
      });
      
      const basePair = new THREE.Mesh(basePairGeometry, basePairMaterial);
      basePair.position.set(0, y, 0);
      basePair.rotation.z = angle;
      
      basePairs.push(basePair);
      segmentGroup.add(basePair);
    }
    
    return {
      group: segmentGroup,
      section: section,
      index: index,
      strand1Material: strand1Material,
      strand2Material: strand2Material,
      basePairs: basePairs,
      completed: false,
      progress: 0
    };
  }

  setupInteractions() {
    // Add click handlers for navigation
    this.container.addEventListener('click', (event) => {
      const rect = this.container.getBoundingClientRect();
      const y = event.clientY - rect.top;
      const segmentIndex = Math.floor((y / rect.height) * this.sections.length);
      
      if (segmentIndex >= 0 && segmentIndex < this.sections.length) {
        this.navigateToSection(segmentIndex);
      }
    });
    
    // Add hover effects
    this.container.addEventListener('mousemove', (event) => {
      this.handleHover(event);
    });
    
    this.container.addEventListener('mouseleave', () => {
      this.clearHover();
    });
  }

  navigateToSection(sectionIndex) {
    const section = this.sections[sectionIndex];
    const targetElement = document.getElementById(section.id);
    
    if (targetElement) {
      console.log(`🧬 Navigating to section: ${section.name}`);
      targetElement.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      });
    }
  }

  handleHover(event) {
    const rect = this.container.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const segmentIndex = Math.floor((y / rect.height) * this.sections.length);
    
    if (segmentIndex >= 0 && segmentIndex < this.sections.length) {
      this.showTooltip(segmentIndex, event.clientX, event.clientY);
      this.highlightSegment(segmentIndex);
    }
  }

  showTooltip(sectionIndex, x, y) {
    // Remove existing tooltip
    const existingTooltip = document.getElementById('dna-tooltip');
    if (existingTooltip) existingTooltip.remove();
    
    const section = this.sections[sectionIndex];
    const tooltip = document.createElement('div');
    tooltip.id = 'dna-tooltip';
    tooltip.className = 'dna-tooltip';
    
    const completionStatus = section.completed ? 'Completed' : 'Not visited';
    const completionIcon = section.completed ? '✅' : '⏳';
    
    tooltip.innerHTML = `
      <div class="tooltip-content">
        <h4>${section.name}</h4>
        <p>${completionIcon} ${completionStatus}</p>
      </div>
    `;
    
    tooltip.style.cssText = `
      position: fixed;
      left: ${x - 120}px;
      top: ${y - 60}px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      padding: 12px;
      z-index: 10001;
      pointer-events: none;
      font-size: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transform: translateX(-50%);
    `;
    
    document.body.appendChild(tooltip);
    
    // Auto-remove after delay
    setTimeout(() => {
      if (tooltip.parentNode) tooltip.remove();
    }, 3000);
  }

  clearHover() {
    const tooltip = document.getElementById('dna-tooltip');
    if (tooltip) tooltip.remove();
    
    // Reset segment highlighting
    this.segments.forEach(segment => {
      if (!segment.completed) {
        segment.strand1Material.opacity = 0.3;
        segment.strand2Material.opacity = 0.3;
      }
    });
  }

  highlightSegment(index) {
    this.segments.forEach((segment, i) => {
      if (i === index && !segment.completed) {
        segment.strand1Material.opacity = 0.6;
        segment.strand2Material.opacity = 0.6;
      } else if (!segment.completed) {
        segment.strand1Material.opacity = 0.3;
        segment.strand2Material.opacity = 0.3;
      }
    });
  }

  setupScrollTracking() {
    console.log('📜 Setting up scroll tracking for DNA navigation...');

    const observerOptions = {
      threshold: [0, 0.25, 0.5, 0.75, 1],
      rootMargin: '-10% 0px -10% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const sectionId = entry.target.id;
        const sectionIndex = this.sections.findIndex(section => section.id === sectionId);

        if (sectionIndex !== -1) {
          const intersectionRatio = entry.intersectionRatio;
          this.updateSectionProgress(sectionIndex, intersectionRatio);

          if (intersectionRatio > 0.5) {
            this.setCurrentSection(sectionIndex);
          }
        }
      });
    }, observerOptions);

    // Observe all sections
    this.sections.forEach(section => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      } else {
        console.warn(`Section element not found: ${section.id}`);
      }
    });
  }

  updateSectionProgress(sectionIndex, progress) {
    if (sectionIndex < 0 || sectionIndex >= this.segments.length) return;

    const segment = this.segments[sectionIndex];
    segment.progress = progress;

    // Update visual progress
    const opacity = Math.max(0.3, progress);
    segment.strand1Material.opacity = opacity;
    segment.strand2Material.opacity = opacity;

    // Mark as completed if fully viewed
    if (progress > 0.8 && !segment.completed) {
      this.completeSection(sectionIndex);
    }

    // Update base pairs opacity
    segment.basePairs.forEach(basePair => {
      basePair.material.opacity = Math.max(0.2, progress * 0.6);
    });
  }

  completeSection(sectionIndex) {
    const segment = this.segments[sectionIndex];
    const section = this.sections[sectionIndex];

    segment.completed = true;
    section.completed = true;

    // Animate completion
    segment.strand1Material.opacity = 1.0;
    segment.strand2Material.opacity = 1.0;

    // Add completion glow effect
    segment.basePairs.forEach(basePair => {
      basePair.material.opacity = 0.8;
      basePair.material.emissive.setHex(0x004400);
    });

    console.log(`✅ DNA Navigation: Section "${section.name}" completed`);
  }

  setCurrentSection(sectionIndex) {
    if (this.currentSection !== sectionIndex) {
      this.currentSection = sectionIndex;

      // Update visual current section indicator
      this.segments.forEach((segment, index) => {
        if (index === sectionIndex) {
          // Highlight current section
          segment.group.scale.setScalar(1.1);
        } else {
          segment.group.scale.setScalar(1.0);
        }
      });
    }
  }

  animate() {
    this.animationId = requestAnimationFrame(() => this.animate());

    const time = Date.now() * 0.001;

    // Gentle rotation of the entire helix
    if (this.dnaHelix) {
      this.dnaHelix.rotation.y = time * 0.1;

      // Subtle floating animation
      this.dnaHelix.position.y = Math.sin(time * 0.5) * 0.2;
    }

    // Animate individual segments based on completion
    this.segments.forEach((segment, index) => {
      if (segment.completed) {
        // Completed segments have subtle pulsing
        const pulse = 1 + Math.sin(time * 2 + index) * 0.05;
        segment.group.scale.setScalar(pulse);
      }
    });

    this.renderer.render(this.scene, this.camera);
  }

  setupResponsive() {
    window.addEventListener('resize', () => {
      const wasMobile = this.isMobile;
      const wasTablet = this.isTablet;

      this.isMobile = window.innerWidth < 768;
      this.isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;

      if (wasMobile !== this.isMobile || wasTablet !== this.isTablet) {
        this.updateResponsiveLayout();
      }

      if (this.renderer && this.camera) {
        const containerRect = this.container.getBoundingClientRect();
        this.camera.aspect = containerRect.width / containerRect.height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(containerRect.width, containerRect.height);
      }
    });
  }

  updateResponsiveLayout() {
    // Update container styles
    this.container.style.cssText = this.getContainerStyles();

    // Recreate DNA helix with new dimensions
    if (this.dnaHelix) {
      this.scene.remove(this.dnaHelix);
      this.createDNAHelix();
    }
  }

  createCSSFallback() {
    console.log('🧬 Creating CSS fallback for DNA navigation...');

    this.container.innerHTML = `
      <div class="dna-fallback">
        ${this.sections.map((section, index) => `
          <div class="dna-segment" data-section="${section.id}" data-index="${index}">
            <div class="segment-indicator" style="background-color: ${section.color}"></div>
            <div class="segment-progress"></div>
          </div>
        `).join('')}
      </div>
    `;

    this.setupCSSFallbackStyles();
    this.setupCSSFallbackInteractions();
  }

  setupCSSFallbackStyles() {
    if (document.getElementById('dna-fallback-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'dna-fallback-styles';
    styles.textContent = `
      .dna-fallback {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 10px;
        gap: 8px;
      }

      .dna-segment {
        flex: 1;
        position: relative;
        cursor: pointer;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .segment-indicator {
        width: 100%;
        height: 100%;
        opacity: 0.3;
        transition: all 0.3s ease;
        border-radius: 20px;
      }

      .segment-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: rgba(255, 255, 255, 0.8);
        transition: width 0.3s ease;
      }

      .dna-segment:hover .segment-indicator {
        opacity: 0.6;
        transform: scale(1.05);
      }

      .dna-segment.completed .segment-indicator {
        opacity: 1;
      }

      .dna-segment.current {
        transform: scale(1.1);
      }
    `;
    document.head.appendChild(styles);
  }

  setupCSSFallbackInteractions() {
    const segments = this.container.querySelectorAll('.dna-segment');

    segments.forEach((segment, index) => {
      segment.addEventListener('click', () => {
        this.navigateToSection(index);
      });

      segment.addEventListener('mouseenter', () => {
        const rect = segment.getBoundingClientRect();
        this.showTooltip(index, rect.right + 10, rect.top + rect.height / 2);
      });
    });

    // Setup scroll tracking for CSS fallback
    this.setupScrollTrackingCSS();
  }

  setupScrollTrackingCSS() {
    const observerOptions = {
      threshold: [0, 0.25, 0.5, 0.75, 1],
      rootMargin: '-10% 0px -10% 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const sectionId = entry.target.id;
        const sectionIndex = this.sections.findIndex(section => section.id === sectionId);

        if (sectionIndex !== -1) {
          const segment = this.container.querySelector(`[data-index="${sectionIndex}"]`);
          if (segment) {
            const progress = entry.intersectionRatio;
            const progressBar = segment.querySelector('.segment-progress');

            progressBar.style.width = `${progress * 100}%`;

            if (progress > 0.8) {
              segment.classList.add('completed');
              this.sections[sectionIndex].completed = true;
            }

            if (progress > 0.5) {
              // Remove current class from all segments
              this.container.querySelectorAll('.dna-segment').forEach(s => s.classList.remove('current'));
              segment.classList.add('current');
            }
          }
        }
      });
    }, observerOptions);

    this.sections.forEach(section => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      }
    });
  }

  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.renderer) {
      this.renderer.dispose();
    }

    if (this.container && this.container.parentNode) {
      this.container.remove();
    }
  }

  // Public methods for external control
  showNavigation() {
    if (this.isMobile) {
      this.container.style.transform = 'translateX(0)';
    }
  }

  hideNavigation() {
    if (this.isMobile) {
      this.container.style.transform = 'translateX(70px)';
    }
  }

  toggleNavigation() {
    if (this.isMobile) {
      const isHidden = this.container.style.transform.includes('translateX(70px)');
      if (isHidden) {
        this.showNavigation();
      } else {
        this.hideNavigation();
      }
    }
  }
}

// Auto-initialize DNA Navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for other components to initialize
  setTimeout(() => {
    console.log('🧬 Initializing DNA Navigation System...');
    window.dnaNavigation = new DNANavigation();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DNANavigation;
}
