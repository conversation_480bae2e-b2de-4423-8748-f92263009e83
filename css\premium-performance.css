/* ===== PREMIUM PERFORMANCE OPTIMIZATIONS ===== */
/* Hardware acceleration and 60fps animations for medical education website */

/* ===== HARDWARE ACCELERATION ===== */
.premium-card,
.info-card,
.testimonial-card,
.safety-card,
.about-card,
.premium-button,
.cta-button,
.magnetic-hover,
.floating-element,
.floating-medical {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  contain: layout style paint;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== OPTIMIZED TRANSITIONS ===== */
.premium-transition {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-duration: var(--duration-300);
  transition-property: transform, opacity, box-shadow;
}

.premium-transition-fast {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-duration: var(--duration-150);
  transition-property: transform;
}

.premium-transition-smooth {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-duration: var(--duration-500);
  transition-property: all;
}

/* ===== PERFORMANCE-OPTIMIZED ANIMATIONS ===== */
@keyframes optimizedFloat {
  0%, 100% { 
    transform: translate3d(0, 0, 0) rotate(0deg);
  }
  33% { 
    transform: translate3d(0, -15px, 0) rotate(1deg);
  }
  66% { 
    transform: translate3d(0, -8px, 0) rotate(-1deg);
  }
}

@keyframes optimizedPulse {
  0%, 100% { 
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  50% { 
    transform: translate3d(0, 0, 0) scale(1.05);
    opacity: 0.8;
  }
}

@keyframes optimizedSlideUp {
  from {
    transform: translate3d(0, 60px, 0);
    opacity: 0;
  }
  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

/* ===== CORE WEB VITALS OPTIMIZATIONS ===== */

/* Reduce Cumulative Layout Shift (CLS) */
.layout-stable {
  contain: layout;
  min-height: 1px; /* Prevent layout shifts */
}

.image-stable {
  aspect-ratio: 16 / 9; /* Maintain aspect ratio */
  object-fit: cover;
  width: 100%;
  height: auto;
}

/* Improve Largest Contentful Paint (LCP) */
.hero-section {
  contain: layout style paint;
}

.hero-title {
  font-display: swap; /* Improve font loading */
  contain: layout style;
}

/* Optimize First Input Delay (FID) */
.interactive-element {
  touch-action: manipulation; /* Improve touch responsiveness */
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  .floating-element,
  .floating-medical {
    animation-duration: 8s; /* Slower animations */
    animation-timing-function: ease-out;
  }
  
  /* Simplify transforms on mobile */
  .premium-card:hover,
  .info-card:hover,
  .testimonial-card:hover {
    transform: translate3d(0, -4px, 0) scale(1.01);
  }
  
  /* Reduce backdrop blur on mobile for better performance */
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  /* Optimize particle animations for mobile */
  .particle-medical {
    animation-duration: 15s; /* Slower particles */
    will-change: transform;
  }
}

/* ===== BATTERY OPTIMIZATION ===== */
@media (prefers-reduced-motion: reduce) {
  /* Disable all animations for users who prefer reduced motion */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .floating-element,
  .floating-medical,
  .particle-medical {
    animation: none !important;
  }
  
  .premium-card,
  .info-card,
  .testimonial-card,
  .safety-card,
  .about-card {
    transform: none !important;
  }
}

/* ===== HIGH REFRESH RATE OPTIMIZATION ===== */
@media (min-resolution: 120dpi) {
  .premium-transition {
    transition-duration: var(--duration-200); /* Faster transitions for high refresh displays */
  }
  
  .floating-element,
  .floating-medical {
    animation-duration: 4s; /* Faster animations for smoother appearance */
  }
}

/* ===== DARK MODE PERFORMANCE ===== */
@media (prefers-color-scheme: dark) {
  /* Optimize for OLED displays */
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    background: rgba(0, 0, 0, 0.8); /* True black for OLED efficiency */
  }
  
  .premium-loader {
    background: rgba(0, 0, 0, 0.95);
  }
}

/* ===== MEMORY OPTIMIZATION ===== */
.memory-efficient {
  contain: strict; /* Isolate element for better memory management */
}

.gpu-optimized {
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: transform;
  backface-visibility: hidden;
}

/* ===== SCROLL PERFORMANCE ===== */
.scroll-optimized {
  contain: layout style paint;
  will-change: transform;
}

/* Optimize scroll-triggered animations */
.scroll-reveal {
  contain: layout;
  will-change: transform, opacity;
}

/* ===== INTERACTION PERFORMANCE ===== */
.hover-optimized {
  will-change: transform, box-shadow;
  contain: layout style paint;
}

.hover-optimized:hover {
  contain: layout style paint;
}

/* ===== LOADING PERFORMANCE ===== */
.lazy-load {
  content-visibility: auto; /* Only render when visible */
  contain-intrinsic-size: 300px; /* Provide size hint */
}

/* ===== CRITICAL RENDERING PATH OPTIMIZATION ===== */
.above-fold {
  contain: layout style;
  will-change: auto; /* Don't over-optimize above-fold content */
}

.below-fold {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* ===== BROWSER-SPECIFIC OPTIMIZATIONS ===== */

/* Chrome/Edge optimizations */
@supports (-webkit-appearance: none) {
  .chrome-optimized {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* Firefox optimizations */
@-moz-document url-prefix() {
  .firefox-optimized {
    will-change: auto; /* Firefox handles will-change differently */
  }
}

/* Safari optimizations */
@supports (-webkit-backdrop-filter: blur(1px)) {
  .safari-optimized {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
  }
}

/* ===== ACCESSIBILITY PERFORMANCE ===== */
@media (prefers-contrast: high) {
  /* Simplify effects for high contrast mode */
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.95);
  }
}

/* ===== PRINT OPTIMIZATION ===== */
@media print {
  /* Remove all animations and effects for printing */
  *,
  *::before,
  *::after {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    filter: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
  
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    background: white;
    border: 1px solid #ccc;
  }
}

/* ===== PERFORMANCE MONITORING ===== */
.performance-monitor {
  /* Add performance monitoring hooks */
  contain: strict;
  will-change: auto;
}

/* Mark critical performance elements */
.critical-performance {
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0);
}
