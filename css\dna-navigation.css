/* ===== DNA NAVIGATION SYSTEM STYLES ===== */
/* Sophisticated scroll-responsive DNA helix navigation */

/* ===== DNA NAVIGATION CONTAINER ===== */
.dna-navigation-container {
  position: fixed;
  z-index: 1000;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--duration-300) var(--ease-premium);
  contain: layout style paint;
  will-change: transform;
}

.dna-navigation-container:hover {
  background: var(--glass-bg-strong);
  border-color: var(--glass-border-strong);
  box-shadow: var(--shadow-multi-layer);
}

/* ===== DNA NAVIGATION TOGGLE BUTTON ===== */
.dna-nav-toggle {
  position: fixed;
  top: 120px;
  right: 15px;
  width: 50px;
  height: 50px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  display: none; /* Hidden by default, shown on mobile via JS */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: all var(--duration-300) var(--ease-premium);
  box-shadow: var(--shadow-premium);
}

.dna-nav-toggle:hover {
  background: var(--glass-bg-strong);
  transform: scale(1.1);
  box-shadow: var(--shadow-multi-layer);
}

.dna-nav-toggle:active {
  transform: scale(0.95);
}

.dna-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===== ENHANCED TOOLTIP STYLES ===== */
.dna-tooltip {
  position: fixed;
  z-index: 10001;
  pointer-events: none;
  font-family: var(--font-primary);
  animation: tooltipSlideIn 0.3s ease-out;
}

.dna-tooltip.enhanced {
  min-width: 280px;
  max-width: 320px;
}

.tooltip-content {
  color: var(--color-text);
}

.tooltip-content.enhanced {
  text-align: left;
}

.tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-header h4 {
  margin: 0;
  font-size: 0.95rem;
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

.section-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.tooltip-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
}

.stat-icon {
  font-size: 0.9rem;
  width: 16px;
  text-align: center;
}

.stat-text {
  color: var(--color-text);
  opacity: 0.9;
}

.tooltip-preview {
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  border-left: 3px solid var(--primary-500);
}

.tooltip-preview p {
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--color-text);
  opacity: 0.8;
}

.tooltip-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.progress-text {
  font-size: 0.7rem;
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  min-width: 30px;
  text-align: right;
}

.tooltip-actions {
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-hint {
  font-size: 0.7rem;
  color: var(--color-text);
  opacity: 0.6;
  font-style: italic;
}

/* ===== LOADING SKELETON STYLES REMOVED ===== */
/* Skeleton loading functionality has been removed for cleaner initialization */

/* ===== ANIMATIONS ===== */
@keyframes tooltipSlideIn {
  from {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes tooltipSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
}

/* skeletonPulse animation removed */

/* ===== PERFORMANCE INDICATOR ===== */
#dna-performance-indicator {
  position: fixed;
  top: 250px;
  right: 10px;
  z-index: 10000;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  font-family: var(--font-mono, monospace);
}

#dna-performance-indicator:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.05);
}

/* ===== DNA SETTINGS PANEL ===== */
.dna-settings-panel {
  font-family: var(--font-primary);
  color: var(--color-text);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.settings-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--color-text);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--primary-500);
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.settings-section {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.settings-section:last-child {
  border-bottom: none;
}

.settings-section h4 {
  margin: 0 0 16px 0;
  font-size: 0.95rem;
  font-weight: var(--font-semibold);
  color: var(--primary-500);
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: var(--font-medium);
  margin-bottom: 4px;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--primary-400);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.setting-label input[type="range"] {
  flex: 1;
  margin: 0 12px;
  accent-color: var(--primary-500);
}

.range-value {
  font-size: 0.8rem;
  color: var(--primary-600);
  font-weight: var(--font-semibold);
  min-width: 30px;
  text-align: right;
}

.setting-description {
  margin: 0;
  font-size: 0.8rem;
  color: var(--color-text);
  opacity: 0.7;
  line-height: 1.4;
  margin-left: 30px;
}

.voice-commands {
  margin-top: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid var(--primary-500);
}

.voice-commands h5 {
  margin: 0 0 8px 0;
  font-size: 0.85rem;
  color: var(--primary-600);
}

.voice-commands ul {
  margin: 0;
  padding-left: 16px;
  font-size: 0.8rem;
  line-height: 1.5;
}

.voice-commands li {
  margin-bottom: 4px;
  color: var(--color-text);
  opacity: 0.8;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.85rem;
}

.shortcut-item kbd {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-family: var(--font-mono, monospace);
  color: var(--primary-600);
  min-width: 60px;
  text-align: center;
}

.analytics-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.analytics-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.analytics-label {
  color: var(--color-text);
  opacity: 0.8;
}

.analytics-value {
  color: var(--primary-600);
  font-weight: var(--font-semibold);
}

.performance-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.performance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.performance-label {
  color: var(--color-text);
  opacity: 0.8;
}

.performance-value {
  color: var(--primary-600);
  font-weight: var(--font-semibold);
  font-family: var(--font-mono, monospace);
}

.action-btn {
  background: var(--primary-500);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 8px;
  margin-bottom: 8px;
}

.action-btn:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
}

.action-btn.primary {
  background: var(--primary-600);
}

.action-btn.danger {
  background: #dc3545;
}

.action-btn.danger:hover {
  background: #c82333;
}

.settings-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

/* ===== NOTIFICATION ANIMATIONS ===== */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ===== CSS FALLBACK STYLES ===== */
.dna-fallback {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  gap: 8px;
}

.dna-segment {
  flex: 1;
  position: relative;
  cursor: pointer;
  border-radius: 20px;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  contain: layout style paint;
}

.dna-segment:hover {
  transform: scale(1.05);
}

.dna-segment.current {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.4);
}

.segment-indicator {
  width: 100%;
  height: 100%;
  opacity: 0.3;
  transition: all var(--duration-300) var(--ease-premium);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.segment-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-500) var(--ease-premium);
}

.dna-segment:hover .segment-indicator::before {
  left: 100%;
}

.dna-segment:hover .segment-indicator {
  opacity: 0.6;
}

.dna-segment.completed .segment-indicator {
  opacity: 1;
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.2);
}

.segment-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  transition: width var(--duration-300) var(--ease-premium);
  border-radius: 0 0 20px 20px;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Desktop Styles */
@media (min-width: 1024px) {
  .dna-navigation-container {
    top: 50%;
    right: 30px;
    width: 100px;
    height: 300px;
    border-radius: 50px;
    transform: translateY(-50%);
  }
  
  .dna-nav-toggle {
    display: none;
  }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .dna-navigation-container {
    top: 50%;
    right: 20px;
    width: 80px;
    height: 250px;
    border-radius: 40px;
    transform: translateY(-50%);
  }
  
  .dna-nav-toggle {
    display: none;
  }
}

/* Mobile Styles */
@media (max-width: 767px) {
  .dna-navigation-container {
    top: 60px;
    right: 10px;
    width: 60px;
    height: 200px;
    border-radius: 30px;
    transform: translateX(70px);
    transition: transform var(--duration-300) var(--ease-premium);
  }
  
  .dna-navigation-container.visible {
    transform: translateX(0);
  }
  
  .dna-nav-toggle {
    display: flex;
  }
  
  .dna-segment {
    border-radius: 15px;
  }
  
  .segment-indicator {
    border-radius: 15px;
  }
}

/* ===== ACCESSIBILITY SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  .dna-navigation-container,
  .dna-nav-toggle,
  .dna-segment,
  .segment-indicator,
  .segment-progress {
    transition: none !important;
    animation: none !important;
  }
  
  .dna-navigation-container {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.95);
  }
  
  .segment-indicator::before {
    display: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .dna-navigation-container {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.8);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .dna-nav-toggle {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.8);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .segment-indicator {
    border: 1px solid rgba(0, 0, 0, 0.5);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .dna-navigation-container {
    background: var(--glass-bg-dark);
    border-color: var(--glass-border-dark);
    box-shadow: var(--glass-shadow-dark);
  }
  
  .dna-nav-toggle {
    background: var(--glass-bg-dark);
    border-color: var(--glass-border-dark);
  }
  
  .tooltip-content {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .tooltip-content h4 {
    color: #60a5fa;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.dna-navigation-container canvas {
  will-change: auto;
  contain: strict;
}

.dna-segment {
  will-change: transform;
  contain: layout style paint;
}

/* ===== PRINT STYLES ===== */
@media print {
  .dna-navigation-container,
  .dna-nav-toggle {
    display: none !important;
  }
}

/* ===== FOCUS STYLES FOR ACCESSIBILITY ===== */
.dna-nav-toggle:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.dna-segment:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== LOADING STATE ===== */
.dna-navigation-container.loading {
  opacity: 0.5;
  pointer-events: none;
}

.dna-navigation-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary-200);
  border-top-color: var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
