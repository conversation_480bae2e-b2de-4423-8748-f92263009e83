/* ===== DNA NAVIGATION SYSTEM STYLES ===== */
/* Sophisticated scroll-responsive DNA helix navigation */

/* ===== DNA NAVIGATION CONTAINER ===== */
.dna-navigation-container {
  position: fixed;
  z-index: 1000;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: all var(--duration-300) var(--ease-premium);
  contain: layout style paint;
  will-change: transform;
}

.dna-navigation-container:hover {
  background: var(--glass-bg-strong);
  border-color: var(--glass-border-strong);
  box-shadow: var(--shadow-multi-layer);
}

/* ===== DNA NAVIGATION TOGGLE BUTTON ===== */
.dna-nav-toggle {
  position: fixed;
  top: 120px;
  right: 15px;
  width: 50px;
  height: 50px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  display: none; /* Hidden by default, shown on mobile via JS */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  transition: all var(--duration-300) var(--ease-premium);
  box-shadow: var(--shadow-premium);
}

.dna-nav-toggle:hover {
  background: var(--glass-bg-strong);
  transform: scale(1.1);
  box-shadow: var(--shadow-multi-layer);
}

.dna-nav-toggle:active {
  transform: scale(0.95);
}

.dna-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===== TOOLTIP STYLES ===== */
.dna-tooltip {
  position: fixed;
  z-index: 10001;
  pointer-events: none;
  font-family: var(--font-primary);
  animation: tooltipFadeIn 0.2s ease-out;
}

.tooltip-content {
  text-align: center;
  color: var(--color-text);
}

.tooltip-content h4 {
  margin: 0 0 4px 0;
  font-size: 0.875rem;
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

.tooltip-content p {
  margin: 0;
  font-size: 0.75rem;
  opacity: 0.8;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== CSS FALLBACK STYLES ===== */
.dna-fallback {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  gap: 8px;
}

.dna-segment {
  flex: 1;
  position: relative;
  cursor: pointer;
  border-radius: 20px;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  contain: layout style paint;
}

.dna-segment:hover {
  transform: scale(1.05);
}

.dna-segment.current {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.4);
}

.segment-indicator {
  width: 100%;
  height: 100%;
  opacity: 0.3;
  transition: all var(--duration-300) var(--ease-premium);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.segment-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-500) var(--ease-premium);
}

.dna-segment:hover .segment-indicator::before {
  left: 100%;
}

.dna-segment:hover .segment-indicator {
  opacity: 0.6;
}

.dna-segment.completed .segment-indicator {
  opacity: 1;
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.2);
}

.segment-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
  transition: width var(--duration-300) var(--ease-premium);
  border-radius: 0 0 20px 20px;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Desktop Styles */
@media (min-width: 1024px) {
  .dna-navigation-container {
    top: 50%;
    right: 30px;
    width: 100px;
    height: 300px;
    border-radius: 50px;
    transform: translateY(-50%);
  }
  
  .dna-nav-toggle {
    display: none;
  }
}

/* Tablet Styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .dna-navigation-container {
    top: 50%;
    right: 20px;
    width: 80px;
    height: 250px;
    border-radius: 40px;
    transform: translateY(-50%);
  }
  
  .dna-nav-toggle {
    display: none;
  }
}

/* Mobile Styles */
@media (max-width: 767px) {
  .dna-navigation-container {
    top: 60px;
    right: 10px;
    width: 60px;
    height: 200px;
    border-radius: 30px;
    transform: translateX(70px);
    transition: transform var(--duration-300) var(--ease-premium);
  }
  
  .dna-navigation-container.visible {
    transform: translateX(0);
  }
  
  .dna-nav-toggle {
    display: flex;
  }
  
  .dna-segment {
    border-radius: 15px;
  }
  
  .segment-indicator {
    border-radius: 15px;
  }
}

/* ===== ACCESSIBILITY SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  .dna-navigation-container,
  .dna-nav-toggle,
  .dna-segment,
  .segment-indicator,
  .segment-progress {
    transition: none !important;
    animation: none !important;
  }
  
  .dna-navigation-container {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(255, 255, 255, 0.95);
  }
  
  .segment-indicator::before {
    display: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .dna-navigation-container {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.8);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .dna-nav-toggle {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.8);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .segment-indicator {
    border: 1px solid rgba(0, 0, 0, 0.5);
  }
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  .dna-navigation-container {
    background: var(--glass-bg-dark);
    border-color: var(--glass-border-dark);
    box-shadow: var(--glass-shadow-dark);
  }
  
  .dna-nav-toggle {
    background: var(--glass-bg-dark);
    border-color: var(--glass-border-dark);
  }
  
  .tooltip-content {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .tooltip-content h4 {
    color: #60a5fa;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.dna-navigation-container canvas {
  will-change: auto;
  contain: strict;
}

.dna-segment {
  will-change: transform;
  contain: layout style paint;
}

/* ===== PRINT STYLES ===== */
@media print {
  .dna-navigation-container,
  .dna-nav-toggle {
    display: none !important;
  }
}

/* ===== FOCUS STYLES FOR ACCESSIBILITY ===== */
.dna-nav-toggle:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.dna-segment:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== LOADING STATE ===== */
.dna-navigation-container.loading {
  opacity: 0.5;
  pointer-events: none;
}

.dna-navigation-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary-200);
  border-top-color: var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
