/* ===== PREMIUM MEDICAL EDUCATION INTERACTIONS ===== */
/* Advanced JavaScript for smooth, professional user experience */

class PremiumInteractions {
  constructor() {
    this.init();
  }

  init() {
    this.setupScrollReveal();
    this.setupSmoothScrolling();
    this.setupParallaxEffects();
    this.setupMagneticElements();
    this.setupAdvancedLoading();
    this.setupPremiumForms();
    this.setupAnimatedCounters();
    this.setupNavbarEffects();
    this.setupPerformanceOptimization();
  }

  // ===== NAVBAR TRANSPARENCY EFFECTS =====
  setupNavbarEffects() {
    const header = document.querySelector('.header');
    if (!header) return;

    let ticking = false;

    const updateNavbar = () => {
      const scrolled = window.pageYOffset;

      if (scrolled > 100) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }

      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateNavbar);
        ticking = true;
      }
    });
  }

  // ===== SCROLL REVEAL ANIMATIONS =====
  setupScrollReveal() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('revealed');

          // Stagger animations for child elements
          const staggerItems = entry.target.querySelectorAll('.stagger-item');
          staggerItems.forEach((item, index) => {
            setTimeout(() => {
              item.classList.add('animate');
            }, index * 100);
          });
        }
      });
    }, observerOptions);

    // Observe elements with reveal classes
    document.querySelectorAll('.reveal-up, .reveal-left, .reveal-right, .reveal-scale').forEach(el => {
      observer.observe(el);
    });

    // ===== FALLBACK MECHANISM FOR ORPHANED STAGGER ITEMS =====
    this.setupStaggerItemsFallback();
  }

  setupStaggerItemsFallback() {
    console.log('🔧 Setting up fallback mechanism for stagger items...');

    // Find all stagger items that might not have proper reveal triggers
    const allStaggerItems = document.querySelectorAll('.stagger-item');
    const orphanedStaggerItems = [];

    allStaggerItems.forEach(item => {
      // Check if the item's parent or ancestor has a reveal class
      let hasRevealParent = false;
      let currentElement = item.parentElement;

      while (currentElement && currentElement !== document.body) {
        if (currentElement.classList.contains('reveal-up') ||
            currentElement.classList.contains('reveal-left') ||
            currentElement.classList.contains('reveal-right') ||
            currentElement.classList.contains('reveal-scale')) {
          hasRevealParent = true;
          break;
        }
        currentElement = currentElement.parentElement;
      }

      if (!hasRevealParent) {
        orphanedStaggerItems.push(item);
      }
    });

    if (orphanedStaggerItems.length > 0) {
      console.warn(`⚠️ Found ${orphanedStaggerItems.length} orphaned stagger items without reveal triggers`);

      // Create fallback observer for orphaned items
      const fallbackObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !entry.target.classList.contains('animate')) {
            console.log('🔧 Fallback: Animating orphaned stagger item');
            entry.target.classList.add('animate');
          }
        });
      }, { threshold: 0.2 });

      // Observe orphaned stagger items directly
      orphanedStaggerItems.forEach(item => {
        fallbackObserver.observe(item);
      });

      // Timeout fallback - animate any remaining invisible stagger items after 5 seconds
      setTimeout(() => {
        const stillInvisibleItems = document.querySelectorAll('.stagger-item:not(.animate)');
        if (stillInvisibleItems.length > 0) {
          console.log(`🔧 Timeout fallback: Animating ${stillInvisibleItems.length} remaining stagger items`);
          stillInvisibleItems.forEach((item, index) => {
            setTimeout(() => {
              item.classList.add('animate');
            }, index * 50);
          });
        }
      }, 5000);
    } else {
      console.log('✅ All stagger items have proper reveal triggers');
    }
  }

  // ===== SMOOTH SCROLLING WITH EASING =====
  setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(anchor.getAttribute('href'));
        
        if (target) {
          const headerOffset = 80;
          const elementPosition = target.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          this.smoothScrollTo(offsetPosition, 1000);
        }
      });
    });
  }

  smoothScrollTo(targetPosition, duration) {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animation = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easeInOutCubic(progress);
      
      window.scrollTo(0, startPosition + distance * ease);
      
      if (timeElapsed < duration) {
        requestAnimationFrame(animation);
      }
    };

    requestAnimationFrame(animation);
  }

  // ===== PARALLAX EFFECTS =====
  setupParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax-element');
    
    if (parallaxElements.length === 0) return;

    let ticking = false;

    const updateParallax = () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(element => {
        const rate = scrolled * -0.5;
        element.style.transform = `translateY(${rate}px)`;
      });
      
      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    });
  }

  // ===== ENHANCED MAGNETIC HOVER EFFECTS =====
  setupMagneticElements() {
    const magneticElements = document.querySelectorAll('.magnetic-hover');
    const magneticRadius = 50; // 50px radius for magnetic effect

    magneticElements.forEach(element => {
      let isInRange = false;

      element.addEventListener('mousemove', (e) => {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const distance = Math.sqrt(
          Math.pow(e.clientX - centerX, 2) + Math.pow(e.clientY - centerY, 2)
        );

        if (distance <= magneticRadius) {
          if (!isInRange) {
            element.classList.add('magnetic-active');
            isInRange = true;
          }

          const x = (e.clientX - centerX) * 0.15;
          const y = (e.clientY - centerY) * 0.15;

          element.style.transform = `translate(${x}px, ${y}px) translateY(-4px)`;
        } else {
          if (isInRange) {
            element.classList.remove('magnetic-active');
            isInRange = false;
            element.style.transform = 'translate(0, 0)';
          }
        }
      });

      element.addEventListener('mouseleave', () => {
        element.classList.remove('magnetic-active');
        isInRange = false;
        element.style.transform = 'translate(0, 0)';
      });

      // Add ripple effect on click
      this.addRippleEffect(element);
    });
  }

  // ===== ENHANCED LOADING SCREEN =====
  setupAdvancedLoading() {
    const loader = document.createElement('div');
    loader.className = 'premium-loader';
    loader.innerHTML = `
      <div class="loading-content">
        <div class="medical-pulse"></div>
        <div class="color-transition-loader"></div>
        <div class="loading-text">
          <h3>Preparing Your Medical Journey</h3>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <p class="loading-percentage">0%</p>
          <p class="loading-status">Initializing...</p>
        </div>
      </div>
    `;

    document.body.appendChild(loader);

    // Loading stages with realistic messages
    const loadingStages = [
      { progress: 20, message: "Loading medical programs..." },
      { progress: 40, message: "Fetching university data..." },
      { progress: 60, message: "Preparing cost calculator..." },
      { progress: 80, message: "Loading testimonials..." },
      { progress: 95, message: "Finalizing interface..." },
      { progress: 100, message: "Ready!" }
    ];

    let currentStage = 0;
    let progress = 0;
    const progressFill = loader.querySelector('.progress-fill');
    const progressText = loader.querySelector('.loading-percentage');
    const statusText = loader.querySelector('.loading-status');

    const updateProgress = () => {
      const targetProgress = loadingStages[currentStage]?.progress || 100;
      const increment = (targetProgress - progress) * 0.1;

      progress += increment;

      if (progress >= targetProgress - 1) {
        progress = targetProgress;
        if (currentStage < loadingStages.length - 1) {
          statusText.textContent = loadingStages[currentStage].message;
          currentStage++;
        }
      }

      progressFill.style.width = `${progress}%`;
      progressText.textContent = `${Math.round(progress)}%`;

      if (progress < 100) {
        requestAnimationFrame(updateProgress);
      } else {
        statusText.textContent = "Complete!";
        setTimeout(() => {
          loader.style.opacity = '0';
          loader.style.transform = 'scale(0.9)';
          setTimeout(() => {
            loader.remove();
            document.body.classList.add('loaded');
            this.createParticleBackground();
          }, 500);
        }, 800);
      }
    };

    // Start loading after a short delay
    setTimeout(() => {
      requestAnimationFrame(updateProgress);
    }, 300);
  }

  // ===== PREMIUM FORM INTERACTIONS =====
  setupPremiumForms() {
    const formInputs = document.querySelectorAll('input, textarea, select');
    
    formInputs.forEach(input => {
      // Floating label effect
      const wrapper = document.createElement('div');
      wrapper.className = 'premium-input-wrapper';
      input.parentNode.insertBefore(wrapper, input);
      wrapper.appendChild(input);
      
      // Add focus effects
      input.addEventListener('focus', () => {
        wrapper.classList.add('focused');
      });
      
      input.addEventListener('blur', () => {
        if (!input.value) {
          wrapper.classList.remove('focused');
        }
      });
      
      // Real-time validation feedback
      input.addEventListener('input', () => {
        this.validateInput(input);
      });
    });
  }

  validateInput(input) {
    const wrapper = input.closest('.premium-input-wrapper');
    
    // Remove existing validation classes
    wrapper.classList.remove('valid', 'invalid');
    
    // Basic validation
    if (input.type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(input.value)) {
        wrapper.classList.add('valid');
      } else if (input.value) {
        wrapper.classList.add('invalid');
      }
    } else if (input.required && input.value) {
      wrapper.classList.add('valid');
    } else if (input.required && !input.value) {
      wrapper.classList.add('invalid');
    }
  }

  // ===== PERFORMANCE OPTIMIZATION =====
  setupPerformanceOptimization() {
    // Debounce scroll events
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(() => {
        this.handleScroll();
      }, 16); // ~60fps
    });

    // Lazy load images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.add('loaded');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  }

  handleScroll() {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;
    
    // Update header transparency
    const header = document.querySelector('.header');
    if (header) {
      if (scrolled > 100) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
    }
  }

  // ===== UTILITY METHODS =====
  createParticles(container, count = 20) {
    for (let i = 0; i < count; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDelay = Math.random() * 6 + 's';
      particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
      container.appendChild(particle);
    }
  }

  addRippleEffect(element) {
    element.addEventListener('click', (e) => {
      // Prevent multiple ripples
      const existingRipple = element.querySelector('.ripple');
      if (existingRipple) {
        existingRipple.remove();
      }

      const ripple = document.createElement('span');
      const rect = element.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height) * 2;
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.classList.add('ripple');

      // Ensure element has relative positioning
      if (getComputedStyle(element).position === 'static') {
        element.style.position = 'relative';
      }

      element.appendChild(ripple);

      setTimeout(() => {
        if (ripple.parentNode) {
          ripple.remove();
        }
      }, 600);
    });
  }

  // ===== ANIMATED COUNTERS =====
  setupAnimatedCounters() {
    const counters = document.querySelectorAll('.metric-number, .stat-number');

    const animateCounter = (element) => {
      const target = parseInt(element.textContent.replace(/[^\d]/g, ''));
      const duration = 2000; // 2 seconds
      const start = performance.now();

      const updateCounter = (currentTime) => {
        const elapsed = currentTime - start;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(target * easeOutCubic);

        // Preserve original formatting
        const originalText = element.textContent;
        const prefix = originalText.match(/^[^\d]*/)[0];
        const suffix = originalText.match(/[^\d]*$/)[0];

        element.textContent = prefix + current + suffix;

        if (progress < 1) {
          requestAnimationFrame(updateCounter);
        }
      };

      requestAnimationFrame(updateCounter);
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !entry.target.dataset.animated) {
          entry.target.dataset.animated = 'true';
          animateCounter(entry.target);
        }
      });
    }, { threshold: 0.5 });

    counters.forEach(counter => observer.observe(counter));
  }

  // ===== PARTICLE BACKGROUND CREATION =====
  createParticleBackground() {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;

    const particleContainer = document.createElement('div');
    particleContainer.className = 'particle-container';
    particleContainer.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    `;

    heroSection.appendChild(particleContainer);

    // Create medical-themed particles
    for (let i = 0; i < 15; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle-medical';
      particle.style.animationDelay = `${Math.random() * 20}s`;
      particleContainer.appendChild(particle);
    }
  }
}

// Initialize premium interactions when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PremiumInteractions();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PremiumInteractions;
}
