/* ===== COMPREHENSIVE DEBUGGING AND ERROR FIXING ===== */
/* This script identifies and fixes common issues with the website */

class WebsiteDebugger {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.fixes = [];
    this.init();
  }

  init() {
    console.log('🔍 Starting comprehensive website debugging...');
    this.checkConsoleErrors();
    this.checkLoadingStates();
    this.check3DComponents();
    this.checkMissingAssets();
    this.fixCommonIssues();
    this.generateReport();
  }

  checkConsoleErrors() {
    console.log('📋 Checking for console errors...');
    
    // Override console.error to capture errors
    const originalError = console.error;
    console.error = (...args) => {
      this.errors.push({
        type: 'console_error',
        message: args.join(' '),
        timestamp: new Date().toISOString()
      });
      originalError.apply(console, args);
    };

    // Check for common error patterns
    window.addEventListener('error', (event) => {
      this.errors.push({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        line: event.lineno,
        timestamp: new Date().toISOString()
      });
    });
  }

  checkLoadingStates() {
    console.log('⏳ Checking for stuck loading states...');
    
    const loadingElements = document.querySelectorAll('.component-loading');
    if (loadingElements.length > 0) {
      this.warnings.push({
        type: 'stuck_loading',
        count: loadingElements.length,
        elements: Array.from(loadingElements).map(el => el.parentElement?.id || 'unknown')
      });

      // Auto-fix stuck loading states
      this.fixStuckLoadingStates(loadingElements);
    }
  }

  check3DComponents() {
    console.log('🎮 Checking 3D components...');
    
    // Check Three.js availability
    if (typeof THREE === 'undefined') {
      this.errors.push({
        type: '3d_library_missing',
        message: 'Three.js library not loaded'
      });
    }

    // Check WebGL support
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      this.warnings.push({
        type: 'webgl_unsupported',
        message: 'WebGL not supported on this device'
      });
    }

    // Check globe container
    const globeContainer = document.getElementById('globe-container');
    if (!globeContainer) {
      this.errors.push({
        type: 'missing_container',
        message: 'Globe container not found'
      });
    } else if (globeContainer.children.length === 0 || globeContainer.querySelector('.component-loading')) {
      this.warnings.push({
        type: 'empty_globe_container',
        message: 'Globe container is empty or still loading'
      });
      this.fixGlobeContainer(globeContainer);
    }
  }

  checkMissingAssets() {
    console.log('🖼️ Checking for missing assets...');
    
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
      img.addEventListener('error', () => {
        this.errors.push({
          type: 'missing_image',
          src: img.src,
          alt: img.alt || 'No alt text'
        });
      });
    });
  }

  fixStuckLoadingStates(loadingElements) {
    console.log('🔧 Fixing stuck loading states...');
    
    loadingElements.forEach((loadingEl, index) => {
      const container = loadingEl.parentElement;
      const containerId = container?.id || `container-${index}`;
      
      setTimeout(() => {
        if (loadingEl.parentNode) {
          loadingEl.style.opacity = '0';
          setTimeout(() => {
            if (loadingEl.parentNode) {
              loadingEl.remove();
              this.createFallbackContent(container, containerId);
            }
          }, 300);
        }
      }, 1000);

      this.fixes.push({
        type: 'loading_state_fixed',
        container: containerId,
        action: 'Removed stuck loading state and added fallback'
      });
    });
  }

  fixGlobeContainer(container) {
    console.log('🌍 Fixing globe container...');
    
    // Clear any existing content
    container.innerHTML = '';
    
    // Try to initialize 3D globe
    if (typeof THREE !== 'undefined') {
      try {
        // Create a simple fallback 3D scene
        this.createSimple3DGlobe(container);
        this.fixes.push({
          type: 'globe_fixed',
          action: 'Created simple 3D globe'
        });
      } catch (error) {
        console.warn('Failed to create 3D globe, using CSS fallback');
        this.createCSSGlobeFallback(container);
        this.fixes.push({
          type: 'globe_fallback',
          action: 'Created CSS globe fallback'
        });
      }
    } else {
      this.createCSSGlobeFallback(container);
      this.fixes.push({
        type: 'globe_fallback',
        action: 'Created CSS globe fallback (Three.js not available)'
      });
    }
  }

  createSimple3DGlobe(container) {
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
    
    renderer.setSize(container.offsetWidth, container.offsetHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    container.appendChild(renderer.domElement);

    // Create simple globe
    const geometry = new THREE.SphereGeometry(5, 32, 32);
    const material = new THREE.MeshPhongMaterial({
      color: 0x2563eb,
      transparent: true,
      opacity: 0.8
    });
    const globe = new THREE.Mesh(geometry, material);
    scene.add(globe);

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 10);
    scene.add(directionalLight);

    camera.position.z = 15;

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);
      globe.rotation.y += 0.01;
      renderer.render(scene, camera);
    };
    animate();
  }

  createCSSGlobeFallback(container) {
    container.innerHTML = `
      <div class="css-globe-fallback">
        <div class="globe-sphere">
          <div class="globe-marker"></div>
        </div>
        <div class="globe-info">
          <h4>FEFU Location</h4>
          <p>Vladivostok, Russia</p>
        </div>
      </div>
    `;

    // Add CSS if not already present
    if (!document.getElementById('css-globe-fallback-styles')) {
      const styles = document.createElement('style');
      styles.id = 'css-globe-fallback-styles';
      styles.textContent = `
        .css-globe-fallback {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          padding: 2rem;
          background: linear-gradient(135deg, #0a0a1a 0%, #1a1a2e 100%);
          border-radius: 12px;
          color: white;
          text-align: center;
        }
        
        .globe-sphere {
          width: 120px;
          height: 120px;
          background: linear-gradient(135deg, #2563eb 0%, #0891b2 100%);
          border-radius: 50%;
          position: relative;
          margin-bottom: 1rem;
          animation: globeRotate 10s linear infinite;
          box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
        }
        
        .globe-marker {
          position: absolute;
          top: 25%;
          right: 30%;
          width: 8px;
          height: 8px;
          background: #ff4444;
          border-radius: 50%;
          animation: markerPulse 2s ease-in-out infinite;
        }
        
        .globe-info h4 {
          margin: 0 0 0.5rem 0;
          color: #2563eb;
          font-size: 1.1rem;
        }
        
        .globe-info p {
          margin: 0;
          opacity: 0.8;
          font-size: 0.9rem;
        }
        
        @keyframes globeRotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        
        @keyframes markerPulse {
          0%, 100% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.5); opacity: 0.6; }
        }
      `;
      document.head.appendChild(styles);
    }
  }

  createFallbackContent(container, containerId) {
    if (!container) return;

    const fallbackContent = {
      'info-cards-container': this.createInfoCardsFallback,
      'campus-gallery': this.createGalleryFallback,
      'advantages-container': this.createAdvantagesFallback
    };

    const fallbackFunction = fallbackContent[containerId];
    if (fallbackFunction) {
      fallbackFunction.call(this, container);
    } else {
      container.innerHTML = `
        <div style="text-align: center; padding: 2rem; color: #666;">
          <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 1rem; color: #2563eb;"></i>
          <h3>Content Loading</h3>
          <p>This section is being prepared for you.</p>
        </div>
      `;
    }
  }

  createInfoCardsFallback(container) {
    container.innerHTML = `
      <div class="info-cards-fallback">
        <h3>MBBS Program Information</h3>
        <div class="quick-info-grid">
          <div class="info-item">
            <strong>Duration:</strong> 6 Years (5+1 Internship)
          </div>
          <div class="info-item">
            <strong>Fees:</strong> ₹4.2L - ₹4.8L per year
          </div>
          <div class="info-item">
            <strong>Medium:</strong> English
          </div>
          <div class="info-item">
            <strong>Recognition:</strong> WHO & NMC Approved
          </div>
        </div>
      </div>
    `;
  }

  generateReport() {
    console.log('📊 Generating debugging report...');
    
    const report = {
      timestamp: new Date().toISOString(),
      errors: this.errors,
      warnings: this.warnings,
      fixes: this.fixes,
      summary: {
        totalErrors: this.errors.length,
        totalWarnings: this.warnings.length,
        totalFixes: this.fixes.length
      }
    };

    console.group('🔍 Website Debugging Report');
    console.log('Summary:', report.summary);
    if (this.errors.length > 0) {
      console.group('❌ Errors Found:');
      this.errors.forEach(error => console.error(error));
      console.groupEnd();
    }
    if (this.warnings.length > 0) {
      console.group('⚠️ Warnings:');
      this.warnings.forEach(warning => console.warn(warning));
      console.groupEnd();
    }
    if (this.fixes.length > 0) {
      console.group('🔧 Fixes Applied:');
      this.fixes.forEach(fix => console.log(fix));
      console.groupEnd();
    }
    console.groupEnd();

    // Store report for debugging
    window.debugReport = report;
    
    return report;
  }
}

// Auto-initialize debugger
document.addEventListener('DOMContentLoaded', () => {
  // Add debug button if in debug mode
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('debug') === 'true') {
    const debugBtn = document.getElementById('debug-btn');
    if (debugBtn) {
      debugBtn.style.display = 'block';
    }
  }

  // Initialize debugger after a short delay to catch loading issues
  setTimeout(() => {
    new WebsiteDebugger();
  }, 2000);
});

// Global function to clear loading states manually
window.clearAllLoadingStates = function() {
  const loadingElements = document.querySelectorAll('.component-loading');
  loadingElements.forEach(el => {
    el.style.opacity = '0';
    setTimeout(() => el.remove(), 300);
  });
  console.log('🔧 Manually cleared all loading states');
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebsiteDebugger;
}
