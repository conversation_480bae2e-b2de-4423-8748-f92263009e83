/* ===== PREMIUM NAVIGATION TESTING SUITE ===== */
/* Comprehensive testing for premium medical navigation system */

class PremiumNavigationTester {
  constructor() {
    this.testResults = [];
    this.init();
  }

  init() {
    console.log('🏥 Starting Premium Navigation testing suite...');
    this.runAllTests();
  }

  async runAllTests() {
    await this.testNavigationInitialization();
    await this.testResponsiveDesign();
    await this.testScrollBehavior();
    await this.testMobileNavigation();
    await this.testSearchFunctionality();
    await this.testAccessibilityFeatures();
    await this.testPerformance();
    await this.testIntegration();
    this.generateReport();
  }

  async testNavigationInitialization() {
    console.log('🏥 Testing navigation initialization...');
    
    // Test navigation instance
    this.addTestResult(
      'Premium Navigation Instance',
      typeof window.premiumNavigation !== 'undefined',
      'Premium Navigation should be initialized'
    );

    // Test navigation container
    const navbar = document.getElementById('premium-navbar');
    this.addTestResult(
      'Navigation Container',
      !!navbar,
      'Premium navbar container should exist'
    );

    if (navbar) {
      // Test fixed positioning
      const styles = getComputedStyle(navbar);
      this.addTestResult(
        'Fixed Positioning',
        styles.position === 'fixed',
        'Navigation should be fixed positioned'
      );

      // Test height optimization
      const height = navbar.offsetHeight;
      this.addTestResult(
        'Height Optimization',
        height <= 60,
        `Navigation height should be ≤60px, actual: ${height}px`
      );

      // Test glassmorphism effects
      const hasBackdropFilter = styles.backdropFilter !== 'none' || styles.webkitBackdropFilter !== 'none';
      this.addTestResult(
        'Glassmorphism Effects',
        hasBackdropFilter,
        'Navigation should have backdrop-filter effects'
      );
    }

    // Test logo and brand
    const logo = document.querySelector('.logo');
    this.addTestResult(
      'Logo Element',
      !!logo,
      'Logo should be present in navigation'
    );

    // Test navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    this.addTestResult(
      'Navigation Links',
      navLinks.length >= 5,
      `Should have at least 5 navigation links, found ${navLinks.length}`
    );
  }

  async testResponsiveDesign() {
    console.log('📱 Testing responsive design...');
    
    const originalWidth = window.innerWidth;
    
    // Test desktop layout
    Object.defineProperty(window, 'innerWidth', { value: 1200, configurable: true });
    window.dispatchEvent(new Event('resize'));
    
    const desktopMenu = document.querySelector('.nav-menu');
    const mobileToggle = document.querySelector('.nav-toggle');
    
    this.addTestResult(
      'Desktop Menu Visibility',
      desktopMenu && getComputedStyle(desktopMenu).display !== 'none',
      'Desktop menu should be visible on large screens'
    );

    // Test mobile layout
    Object.defineProperty(window, 'innerWidth', { value: 600, configurable: true });
    window.dispatchEvent(new Event('resize'));
    
    this.addTestResult(
      'Mobile Toggle Visibility',
      mobileToggle && getComputedStyle(mobileToggle).display !== 'none',
      'Mobile toggle should be visible on small screens'
    );

    // Test tablet layout
    Object.defineProperty(window, 'innerWidth', { value: 800, configurable: true });
    window.dispatchEvent(new Event('resize'));
    
    // Restore original width
    Object.defineProperty(window, 'innerWidth', { value: originalWidth, configurable: true });
    window.dispatchEvent(new Event('resize'));

    // Test touch targets
    const touchTargets = document.querySelectorAll('.nav-link, .nav-action-btn, .mobile-nav-link');
    let validTouchTargets = 0;
    
    touchTargets.forEach(target => {
      const rect = target.getBoundingClientRect();
      if (rect.width >= 44 && rect.height >= 44) {
        validTouchTargets++;
      }
    });

    this.addTestResult(
      'Touch Target Size',
      validTouchTargets > 0,
      `${validTouchTargets} elements meet 44px minimum touch target size`
    );
  }

  async testScrollBehavior() {
    console.log('📜 Testing scroll behavior...');
    
    const navbar = document.getElementById('premium-navbar');
    if (!navbar) {
      this.addTestResult('Scroll Behavior', false, 'Navbar not found');
      return;
    }

    // Test scroll detection
    const hasScrollListener = typeof window.premiumNavigation.handleScroll === 'function';
    this.addTestResult(
      'Scroll Detection',
      hasScrollListener,
      'Navigation should have scroll detection'
    );

    // Test active section tracking
    const hasActiveTracking = typeof window.premiumNavigation.setActiveSection === 'function';
    this.addTestResult(
      'Active Section Tracking',
      hasActiveTracking,
      'Navigation should track active sections'
    );

    // Test smooth scrolling
    const hasSmoothScroll = typeof window.premiumNavigation.navigateToSection === 'function';
    this.addTestResult(
      'Smooth Scrolling',
      hasSmoothScroll,
      'Navigation should support smooth scrolling'
    );

    // Test scroll offset
    const sections = document.querySelectorAll('section[id]');
    let hasScrollMargin = false;
    
    sections.forEach(section => {
      const styles = getComputedStyle(section);
      if (styles.scrollMarginTop && styles.scrollMarginTop !== '0px') {
        hasScrollMargin = true;
      }
    });

    this.addTestResult(
      'Scroll Margin Offset',
      hasScrollMargin,
      'Sections should have scroll margin for fixed navbar'
    );
  }

  async testMobileNavigation() {
    console.log('📱 Testing mobile navigation...');
    
    // Test mobile overlay
    const mobileOverlay = document.getElementById('mobile-nav-overlay');
    this.addTestResult(
      'Mobile Overlay',
      !!mobileOverlay,
      'Mobile navigation overlay should exist'
    );

    // Test mobile toggle functionality
    const navToggle = document.getElementById('nav-toggle');
    this.addTestResult(
      'Mobile Toggle Button',
      !!navToggle,
      'Mobile navigation toggle should exist'
    );

    if (navToggle && window.premiumNavigation) {
      // Test toggle functionality
      const hasToggleMethod = typeof window.premiumNavigation.toggleMobileMenu === 'function';
      this.addTestResult(
        'Mobile Toggle Functionality',
        hasToggleMethod,
        'Mobile navigation should have toggle functionality'
      );
    }

    // Test mobile navigation links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    this.addTestResult(
      'Mobile Navigation Links',
      mobileNavLinks.length >= 5,
      `Mobile navigation should have links, found ${mobileNavLinks.length}`
    );

    // Test mobile CTA button
    const mobileCTA = document.querySelector('.mobile-cta-button');
    this.addTestResult(
      'Mobile CTA Button',
      !!mobileCTA,
      'Mobile navigation should have CTA button'
    );

    // Test hamburger animation
    const hamburgerLines = document.querySelectorAll('.hamburger-line');
    this.addTestResult(
      'Hamburger Animation',
      hamburgerLines.length === 3,
      'Hamburger menu should have 3 lines for animation'
    );
  }

  async testSearchFunctionality() {
    console.log('🔍 Testing search functionality...');
    
    // Test search button
    const searchBtn = document.querySelector('.search-btn');
    this.addTestResult(
      'Search Button',
      !!searchBtn,
      'Search button should be present'
    );

    // Test search overlay
    const searchOverlay = document.getElementById('search-overlay');
    this.addTestResult(
      'Search Overlay',
      !!searchOverlay,
      'Search overlay should exist'
    );

    // Test search input
    const searchInput = document.querySelector('.search-input');
    this.addTestResult(
      'Search Input',
      !!searchInput,
      'Search input field should exist'
    );

    // Test search suggestions
    const searchSuggestions = document.querySelectorAll('.search-suggestion');
    this.addTestResult(
      'Search Suggestions',
      searchSuggestions.length > 0,
      `Search suggestions should be available, found ${searchSuggestions.length}`
    );

    // Test search functionality
    if (window.premiumNavigation) {
      const hasSearchMethod = typeof window.premiumNavigation.performSearch === 'function';
      this.addTestResult(
        'Search Functionality',
        hasSearchMethod,
        'Search functionality should be implemented'
      );
    }
  }

  async testAccessibilityFeatures() {
    console.log('♿ Testing accessibility features...');
    
    // Test ARIA labels
    const navToggle = document.getElementById('nav-toggle');
    if (navToggle) {
      const hasAriaLabel = navToggle.hasAttribute('aria-label');
      const hasAriaExpanded = navToggle.hasAttribute('aria-expanded');
      
      this.addTestResult(
        'ARIA Labels',
        hasAriaLabel && hasAriaExpanded,
        'Navigation toggle should have proper ARIA attributes'
      );
    }

    // Test keyboard navigation
    const focusableElements = document.querySelectorAll(
      '.nav-link, .nav-action-btn, .mobile-nav-link, .search-input'
    );
    
    let keyboardAccessible = 0;
    focusableElements.forEach(element => {
      if (element.tabIndex >= 0 || element.tagName === 'A' || element.tagName === 'BUTTON' || element.tagName === 'INPUT') {
        keyboardAccessible++;
      }
    });

    this.addTestResult(
      'Keyboard Navigation',
      keyboardAccessible > 0,
      `${keyboardAccessible} elements are keyboard accessible`
    );

    // Test skip link
    const skipLink = document.querySelector('.skip-link');
    this.addTestResult(
      'Skip Link',
      !!skipLink,
      'Skip to main content link should be present'
    );

    // Test focus management
    if (window.premiumNavigation) {
      const hasFocusManagement = typeof window.premiumNavigation.openMobileMenu === 'function';
      this.addTestResult(
        'Focus Management',
        hasFocusManagement,
        'Navigation should manage focus properly'
      );
    }

    // Test color contrast (basic check)
    const navbar = document.getElementById('premium-navbar');
    if (navbar) {
      const styles = getComputedStyle(navbar);
      const hasGoodContrast = styles.color !== styles.backgroundColor;
      
      this.addTestResult(
        'Color Contrast',
        hasGoodContrast,
        'Navigation should have sufficient color contrast'
      );
    }
  }

  async testPerformance() {
    console.log('⚡ Testing performance...');
    
    // Test hardware acceleration
    const navbar = document.getElementById('premium-navbar');
    if (navbar) {
      const styles = getComputedStyle(navbar);
      const hasWillChange = styles.willChange !== 'auto';
      
      this.addTestResult(
        'Hardware Acceleration',
        hasWillChange,
        'Navigation should use hardware acceleration'
      );
    }

    // Test transition performance
    const animatedElements = document.querySelectorAll('.nav-link, .nav-action-btn, .mobile-nav-link');
    let hasOptimizedTransitions = 0;
    
    animatedElements.forEach(element => {
      const styles = getComputedStyle(element);
      if (styles.transition && styles.transition !== 'none') {
        hasOptimizedTransitions++;
      }
    });

    this.addTestResult(
      'Smooth Transitions',
      hasOptimizedTransitions > 0,
      `${hasOptimizedTransitions} elements have smooth transitions`
    );

    // Test image optimization
    const logo = document.querySelector('.logo');
    if (logo) {
      const hasLazyLoading = logo.hasAttribute('loading');
      this.addTestResult(
        'Image Optimization',
        hasLazyLoading,
        'Logo should have optimized loading'
      );
    }
  }

  async testIntegration() {
    console.log('🔗 Testing integration with other components...');
    
    // Test DNA navigation integration
    const dnaNavigation = document.getElementById('dna-navigation');
    this.addTestResult(
      'DNA Navigation Integration',
      !!dnaNavigation,
      'DNA navigation should be present and positioned correctly'
    );

    // Test component tests integration
    const hasComponentTests = typeof window.testComponents === 'function';
    this.addTestResult(
      'Component Tests Integration',
      hasComponentTests,
      'Component testing should be available'
    );

    // Test glassmorphism integration
    const glassmorphismElements = document.querySelectorAll('.premium-navbar, .mobile-nav-overlay, .search-overlay');
    let hasGlassmorphism = 0;
    
    glassmorphismElements.forEach(element => {
      const styles = getComputedStyle(element);
      if (styles.backdropFilter !== 'none' || styles.webkitBackdropFilter !== 'none') {
        hasGlassmorphism++;
      }
    });

    this.addTestResult(
      'Glassmorphism Integration',
      hasGlassmorphism > 0,
      `${hasGlassmorphism} elements use glassmorphism effects`
    );

    // Test existing functionality preservation
    const staggerItems = document.querySelectorAll('.stagger-item');
    this.addTestResult(
      'Stagger Animations Preserved',
      staggerItems.length > 0,
      `${staggerItems.length} stagger items preserved`
    );
  }

  addTestResult(testName, passed, description) {
    this.testResults.push({
      name: testName,
      passed,
      description,
      timestamp: new Date().toISOString()
    });
  }

  generateReport() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const passRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.group('🏥 Premium Navigation Testing Report');
    console.log(`Overall: ${passedTests}/${totalTests} tests passed (${passRate}%)`);
    
    const categories = [
      'Initialization',
      'Responsive',
      'Scroll',
      'Mobile',
      'Search',
      'Accessibility',
      'Performance',
      'Integration'
    ];

    categories.forEach(category => {
      const categoryTests = this.testResults.filter(test => 
        test.name.toLowerCase().includes(category.toLowerCase())
      );
      
      if (categoryTests.length > 0) {
        const categoryPassed = categoryTests.filter(test => test.passed).length;
        console.group(`📋 ${category} (${categoryPassed}/${categoryTests.length})`);
        categoryTests.forEach(test => {
          const icon = test.passed ? '✅' : '❌';
          console.log(`${icon} ${test.name}: ${test.description}`);
        });
        console.groupEnd();
      }
    });
    
    console.groupEnd();
    
    // Store results globally
    window.premiumNavigationTestResults = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: totalTests - passedTests,
        passRate: passRate + '%'
      },
      details: this.testResults
    };
    
    // Show visual indicator
    this.showNavigationTestIndicator(passRate);
    
    return this.testResults;
  }

  showNavigationTestIndicator(passRate) {
    const indicator = document.createElement('div');
    indicator.id = 'nav-test-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 350px;
      right: 10px;
      z-index: 10000;
      padding: 12px 16px;
      border-radius: 12px;
      color: white;
      font-size: 12px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      ${passRate >= 95 ? 'background: rgba(40, 167, 69, 0.8);' : 
        passRate >= 80 ? 'background: rgba(255, 193, 7, 0.8); color: #000;' : 
        'background: rgba(220, 53, 69, 0.8);'}
    `;
    
    indicator.innerHTML = `
      <div>🏥 Nav: ${passRate}%</div>
      <div style="font-size: 10px; opacity: 0.8;">Click for details</div>
    `;
    
    indicator.addEventListener('click', () => {
      console.log('Premium Navigation Test Results:', window.premiumNavigationTestResults);
    });
    
    document.body.appendChild(indicator);
    
    // Auto-hide after 15 seconds
    setTimeout(() => {
      if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => indicator.remove(), 300);
      }
    }, 15000);
  }
}

// Auto-run navigation tests when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Wait for navigation to initialize
  setTimeout(() => {
    new PremiumNavigationTester();
  }, 3000);
});

// Manual test trigger
window.testPremiumNavigation = function() {
  new PremiumNavigationTester();
};

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PremiumNavigationTester;
}
