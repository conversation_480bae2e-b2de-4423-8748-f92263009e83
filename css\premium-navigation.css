/* ===== PREMIUM MEDICAL NAVIGATION BAR ===== */
/* Modern, compact, and highly optimized navigation for medical education website */

/* ===== NAVIGATION VARIABLES ===== */
:root {
  --nav-height: 60px;
  --nav-height-mobile: 56px;
  --nav-bg: rgba(255, 255, 255, 0.95);
  --nav-bg-scroll: rgba(255, 255, 255, 0.98);
  --nav-border: rgba(37, 99, 235, 0.1);
  --nav-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  --nav-shadow-scroll: 0 4px 30px rgba(0, 0, 0, 0.12);
  --nav-text: #1e293b;
  --nav-text-hover: var(--primary-600);
  --nav-backdrop: blur(20px);
  --nav-transition: all 0.3s var(--ease-premium);
}

/* ===== MAIN NAVIGATION CONTAINER ===== */
.premium-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--nav-height);
  background: var(--nav-bg);
  backdrop-filter: var(--nav-backdrop);
  -webkit-backdrop-filter: var(--nav-backdrop);
  border-bottom: 1px solid var(--nav-border);
  box-shadow: var(--nav-shadow);
  z-index: 1000;
  transition: var(--nav-transition);
  will-change: transform, background-color, box-shadow;
}

.premium-navbar.scrolled {
  background: var(--nav-bg-scroll);
  box-shadow: var(--nav-shadow-scroll);
  border-bottom-color: rgba(37, 99, 235, 0.15);
}

.premium-navbar.hidden {
  transform: translateY(-100%);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
}

/* ===== LOGO & BRAND ===== */
.nav-brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  z-index: 1001;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  transition: var(--nav-transition);
}

.logo-container:hover {
  transform: scale(1.02);
}

.logo {
  width: 36px;
  height: 36px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: var(--font-bold);
  color: var(--primary-600);
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: 0.75rem;
  color: var(--secondary-500);
  font-weight: var(--font-medium);
  line-height: 1;
  opacity: 0.8;
}

/* ===== DESKTOP NAVIGATION MENU ===== */
.nav-menu {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
  margin: 0 40px;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 8px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  text-decoration: none;
  color: var(--nav-text);
  font-size: 0.9rem;
  font-weight: var(--font-medium);
  border-radius: 8px;
  transition: var(--nav-transition);
  white-space: nowrap;
  overflow: hidden;
}

.nav-link:hover {
  color: var(--nav-text-hover);
  background: rgba(37, 99, 235, 0.05);
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--primary-600);
  background: rgba(37, 99, 235, 0.1);
}

.link-text {
  position: relative;
  z-index: 1;
}

.link-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-600);
  border-radius: 1px;
  transition: var(--nav-transition);
  transform: translateX(-50%);
}

.nav-link.active .link-indicator {
  width: 80%;
}

.nav-link:hover .link-indicator {
  width: 60%;
}

/* ===== NAVIGATION ACTIONS ===== */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.nav-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 1px solid rgba(37, 99, 235, 0.2);
  border-radius: 8px;
  color: var(--nav-text);
  cursor: pointer;
  transition: var(--nav-transition);
  font-size: 0.9rem;
}

.nav-action-btn:hover {
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.3);
  color: var(--primary-600);
  transform: translateY(-1px);
}

.nav-action-btn:active {
  transform: translateY(0);
}

/* ===== LANGUAGE SELECTOR ===== */
.language-selector {
  position: relative;
}

.lang-btn {
  width: auto;
  padding: 0 12px;
  gap: 6px;
}

.lang-text {
  font-size: 0.8rem;
  font-weight: var(--font-semibold);
}

.lang-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 120px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  box-shadow: var(--shadow-premium);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--nav-transition);
  z-index: 1002;
}

.language-selector:hover .lang-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-option {
  display: block;
  padding: 10px 16px;
  color: var(--color-text);
  text-decoration: none;
  font-size: 0.85rem;
  transition: var(--nav-transition);
  border-radius: 6px;
  margin: 4px;
}

.lang-option:hover {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary-600);
}

.lang-option.active {
  background: rgba(37, 99, 235, 0.15);
  color: var(--primary-600);
  font-weight: var(--font-semibold);
}

/* ===== CTA BUTTON ===== */
.cta-button.premium {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-500));
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: var(--font-semibold);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  transition: var(--nav-transition);
  white-space: nowrap;
}

.cta-button.premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.4);
}

.cta-button.premium:active {
  transform: translateY(-1px);
}

.cta-text {
  font-size: 0.9rem;
}

/* ===== MOBILE MENU TOGGLE ===== */
.nav-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: var(--nav-transition);
}

.nav-toggle:hover {
  background: rgba(37, 99, 235, 0.1);
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background: var(--nav-text);
  border-radius: 1px;
  transition: var(--nav-transition);
  transform-origin: center;
}

.hamburger-line:not(:last-child) {
  margin-bottom: 4px;
}

.nav-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== QUICK INFO BAR ===== */
.quick-info-bar {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-500));
  color: white;
  z-index: 999;
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.quick-info-bar.visible {
  transform: translateY(0);
}

.quick-info-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 8px 24px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.quick-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
}

.info-label {
  opacity: 0.9;
}

.info-value {
  font-weight: var(--font-semibold);
}

.quick-info-close {
  position: absolute;
  right: 24px;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: var(--nav-transition);
}

.quick-info-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 1024px) {
  .nav-container {
    padding: 0 20px;
  }
  
  .nav-menu {
    margin: 0 20px;
  }
  
  .nav-links {
    gap: 4px;
  }
  
  .nav-link {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
  
  .brand-text {
    font-size: 1.1rem;
  }
  
  .brand-subtitle {
    font-size: 0.7rem;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .premium-navbar {
    height: var(--nav-height-mobile);
  }
  
  .nav-container {
    padding: 0 16px;
  }
  
  .nav-menu,
  .nav-actions .nav-action-btn:not(.search-btn),
  .language-selector,
  .accessibility-btn {
    display: none;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .logo {
    width: 32px;
    height: 32px;
  }
  
  .brand-text {
    font-size: 1rem;
  }
  
  .brand-subtitle {
    display: none;
  }
  
  .cta-button.premium {
    padding: 8px 16px;
    font-size: 0.8rem;
  }
  
  .cta-text {
    display: none;
  }
  
  .quick-info-content {
    flex-direction: column;
    gap: 8px;
    padding: 12px 16px;
  }
  
  .quick-info-item {
    font-size: 0.75rem;
  }
}

/* Very Small Mobile */
@media (max-width: 480px) {
  .nav-container {
    padding: 0 12px;
  }
  
  .brand-info {
    gap: 0;
  }
  
  .nav-actions {
    gap: 4px;
  }
  
  .nav-action-btn {
    width: 36px;
    height: 36px;
  }
  
  .cta-button.premium {
    padding: 6px 12px;
  }
}

/* ===== MOBILE NAVIGATION OVERLAY ===== */
.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s var(--ease-premium);
  overflow-y: auto;
}

.mobile-nav-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-nav-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.mobile-nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 30px;
}

.mobile-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-logo {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.mobile-brand-text {
  font-size: 1.1rem;
  font-weight: var(--font-bold);
  color: var(--primary-600);
}

.mobile-nav-close {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: var(--color-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--nav-transition);
}

.mobile-nav-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.mobile-nav-links {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 30px;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--color-text);
  text-decoration: none;
  font-size: 1rem;
  font-weight: var(--font-medium);
  transition: var(--nav-transition);
  transform: translateX(-20px);
  opacity: 0;
  animation: slideInLeft 0.4s ease forwards;
}

.mobile-nav-link:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav-link:nth-child(2) { animation-delay: 0.15s; }
.mobile-nav-link:nth-child(3) { animation-delay: 0.2s; }
.mobile-nav-link:nth-child(4) { animation-delay: 0.25s; }
.mobile-nav-link:nth-child(5) { animation-delay: 0.3s; }
.mobile-nav-link:nth-child(6) { animation-delay: 0.35s; }

.mobile-nav-link:hover {
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.3);
  color: var(--primary-600);
  transform: translateX(4px);
}

.mobile-nav-link.active {
  background: rgba(37, 99, 235, 0.15);
  border-color: rgba(37, 99, 235, 0.4);
  color: var(--primary-600);
}

.mobile-nav-link i {
  width: 20px;
  text-align: center;
  opacity: 0.8;
}

.mobile-nav-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.mobile-action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: var(--color-text);
  cursor: pointer;
  transition: var(--nav-transition);
  font-size: 0.8rem;
}

.mobile-action-btn:hover {
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.3);
  color: var(--primary-600);
}

.mobile-action-btn i {
  font-size: 1.2rem;
}

.mobile-nav-cta {
  margin-top: auto;
}

.mobile-cta-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-500));
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: var(--font-semibold);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
  transition: var(--nav-transition);
}

.mobile-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(37, 99, 235, 0.4);
}

/* ===== SEARCH OVERLAY ===== */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  z-index: 1002;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s var(--ease-premium);
  overflow-y: auto;
}

.search-overlay.active {
  opacity: 1;
  visibility: visible;
}

.search-container {
  max-width: 600px;
  margin: 80px auto 0;
  padding: 40px 20px;
}

.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}

.search-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  color: var(--primary-600);
}

.search-close {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: var(--color-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--nav-transition);
}

.search-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.search-form {
  position: relative;
  margin-bottom: 40px;
}

.search-input {
  width: 100%;
  padding: 16px 60px 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: var(--color-text);
  font-size: 1.1rem;
  transition: var(--nav-transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  background: rgba(255, 255, 255, 0.15);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-submit {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  background: var(--primary-600);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: var(--nav-transition);
}

.search-submit:hover {
  background: var(--primary-700);
  transform: translateY(-50%) scale(1.05);
}

.search-suggestions {
  margin-top: 30px;
}

.suggestion-category h4 {
  margin: 0 0 16px 0;
  font-size: 1rem;
  font-weight: var(--font-semibold);
  color: var(--color-text);
  opacity: 0.8;
}

.search-suggestion {
  display: block;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--color-text);
  text-decoration: none;
  margin-bottom: 8px;
  transition: var(--nav-transition);
}

.search-suggestion:hover {
  background: rgba(37, 99, 235, 0.1);
  border-color: rgba(37, 99, 235, 0.3);
  color: var(--primary-600);
  transform: translateX(4px);
}

/* ===== BREADCRUMB NAVIGATION ===== */
.breadcrumb-container {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 0;
}

.breadcrumb-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 0.85rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 8px;
  color: rgba(255, 255, 255, 0.4);
}

.breadcrumb-item a {
  color: var(--color-text);
  text-decoration: none;
  transition: var(--nav-transition);
}

.breadcrumb-item a:hover {
  color: var(--primary-600);
}

.breadcrumb-item.active {
  color: var(--primary-600);
  font-weight: var(--font-medium);
}

/* ===== ANIMATIONS ===== */
@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .premium-navbar,
  .nav-link,
  .nav-action-btn,
  .mobile-nav-link,
  .search-suggestion {
    transition: none !important;
    animation: none !important;
  }

  .mobile-nav-link {
    transform: none !important;
    opacity: 1 !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  .premium-navbar {
    background: rgba(255, 255, 255, 0.98);
    border-bottom: 2px solid #000;
  }

  .nav-link,
  .nav-action-btn {
    border: 2px solid #000;
  }

  .nav-link:hover,
  .nav-action-btn:hover {
    background: #000;
    color: #fff;
  }
}

/* ===== MAIN CONTENT OFFSET ===== */
#main-content {
  padding-top: var(--nav-height);
}

.hero-section {
  min-height: calc(100vh - var(--nav-height));
}

/* Smooth scroll offset for anchor links */
section[id] {
  scroll-margin-top: calc(var(--nav-height) + 20px);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  #main-content {
    padding-top: var(--nav-height-mobile);
  }

  .hero-section {
    min-height: calc(100vh - var(--nav-height-mobile));
  }

  section[id] {
    scroll-margin-top: calc(var(--nav-height-mobile) + 20px);
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .premium-navbar,
  .mobile-nav-overlay,
  .search-overlay {
    display: none !important;
  }

  #main-content {
    padding-top: 0 !important;
  }
}
